<?php return array (
  0 => 
  array (
    'id' => 5866,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号时要减速慢行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112465364.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '交警面对你，单手上下摆动，意思是注意减速。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  1 => 
  array (
    'id' => 5867,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号时允许在路口向右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112462477.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '右手朝地表示不能直走，左手向右表示要右转，组合起来就是向右转弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  2 => 
  array (
    'id' => 5868,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号时可以直行通过。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112465943.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '对你来说是停车等待，因为交警的脸没有看你，交警的脸没有看你，就表示要停车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  3 => 
  array (
    'id' => 5869,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号可以左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112462257.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '对你来说是停车等待；主要看交警脸的朝向，对着交警脸方向的车辆是左转弯待转信号！',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  4 => 
  array (
    'id' => 5870,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出的是左转弯待转手势信号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112461102.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意警察的脸，然后观察手上下摆动，对于警察脸看的方向上的驾驶员来说是左转弯待转！',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  5 => 
  array (
    'id' => 5871,
    'typeId' => '3',
    'type' => 0,
    'subject' => '看到这种手势信号时可以向左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112469399.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '右手掌向左边，表示左边道上的车停车，然后左手向左指并且脸部朝向你，意思是这条车道上车可以左转。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  6 => 
  array (
    'id' => 5872,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号可以直行通过。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112462832.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '先看交警的脸对着谁，这个手势是左转弯。对你来说就是停车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  7 => 
  array (
    'id' => 5873,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势信号可以向左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112461718.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '右手掌向左边，表示左边道上的车停车，然后左手向左指并且脸部朝向你，意思是这条车道上车可以左转。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  8 => 
  array (
    'id' => 5874,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出这种手势可以向左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112462589.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '警察的手势不是给你看的，没看人家没看你，看的是右侧。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  9 => 
  array (
    'id' => 5875,
    'typeId' => '3',
    'type' => 0,
    'subject' => '看到交通警察这种姿势时可以直行通过。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112467374.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '交警站着不动，看绿灯直行就可以了。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  10 => 
  array (
    'id' => 5876,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出的是右转弯手势信号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112469548.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图为直行辅助手势。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  11 => 
  array (
    'id' => 5877,
    'typeId' => '3',
    'type' => 0,
    'subject' => '交通警察发出的是禁止通行手势信号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112468122.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '左手过头勿前行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  12 => 
  array (
    'id' => 5878,
    'typeId' => '3',
    'type' => 1,
    'subject' => '看到这种手势信号时怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112457855.jpg',
    'optionA' => '直行通过路口',
    'optionB' => '靠路边停车',
    'optionC' => '进入左弯待转区',
    'optionD' => '在路口向右转弯',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '交警的面部对着哪个方向就是在指挥哪个方向的车，伸右手向左拐弯，伸左手向右拐弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  13 => 
  array (
    'id' => 5879,
    'typeId' => '3',
    'type' => 1,
    'subject' => '看到这种手势信号时怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112454559.jpg',
    'optionA' => '在路口向左转弯',
    'optionB' => '停车等待',
    'optionC' => '在路口直行',
    'optionD' => '进入左弯待转区',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '交警的面部对着哪个方向就是在指挥哪个方向的车，交警面部对着的那个方向的车左转，注意其右手，明显的往我们的右方摆动，对我们来说，就需要停车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  14 => 
  array (
    'id' => 5880,
    'typeId' => '3',
    'type' => 1,
    'subject' => '看到这种手势信号时怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112453872.jpg',
    'optionA' => '直行通过路口',
    'optionB' => '停车等待',
    'optionC' => '在路口向右转弯',
    'optionD' => '在路口向左转弯',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '交警的面部对着哪个方向就是在指挥哪个方向的车，所以对于这张图片来说，就是在指示交警面部对着的那个方向的车左转，对你来说需要停车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  15 => 
  array (
    'id' => 5881,
    'typeId' => '3',
    'type' => 1,
    'subject' => '看到这种手势信号时怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112459375.jpg',
    'optionA' => '直行通过路口',
    'optionB' => '停在停止线外等待',
    'optionC' => '在路口向左转弯',
    'optionD' => '在路口减速慢行',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '交警左手摆动就是左转弯，右手摆动就是右转弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  16 => 
  array (
    'id' => 5882,
    'typeId' => '3',
    'type' => 1,
    'subject' => '看到这种手势信号时怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112451431.jpg',
    'optionA' => '停车等待',
    'optionB' => '直行通过路口',
    'optionC' => '在路口向右转弯',
    'optionD' => '在路口向左转弯',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '举手朝向您，无其他动作时是让告诉您停止。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  17 => 
  array (
    'id' => 5883,
    'typeId' => '3',
    'type' => 0,
    'subject' => '按照下图红框内的标志，机动车应当在B区域内行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112454608.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图片分析：机动车应在A区域内行驶，B区域是非机动车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  18 => 
  array (
    'id' => 5884,
    'typeId' => '3',
    'type' => 0,
    'subject' => '图中红框内行驶车辆存在交通违法行为。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112454084.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '图片分析：导流线的形式主要为一个或几个根据路口地形设置的白色V形线或斜纹线区域，表示车辆必须按规定的路线行驶，不得压线或越线行驶。主要用于过宽、不规则或行驶条件比较复杂的交叉路口,立体交叉的匝道口或其他特殊地点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  19 => 
  array (
    'id' => 5885,
    'typeId' => '3',
    'type' => 1,
    'subject' => '图中红框内所示车辆可以怎样行驶？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112456885.jpg',
    'optionA' => '不可左转弯',
    'optionB' => '可以右转，但要避让同向直行车辆',
    'optionC' => '可以左转，但要避让对向直行车辆',
    'optionD' => '可以直行',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '图片分析：图中红框内所示车辆可以左转，但要避让对向直行车辆行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  20 => 
  array (
    'id' => 5886,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面菱形块虚线警告前方道路要减速慢行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112458349.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '车行道纵向减速标线为一组平行于车行道分界线的菱形块虚线。在车行道纵向减速标线的起始位置，设置30m的渐变段，因此本题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  21 => 
  array (
    'id' => 5887,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中黄色斜线填充标记警告前方有固定性障碍物。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112456679.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '用以指示路面有固定性障碍物，警告车辆驾驶人谨慎行车，引导交通流顺畅驶离障碍物区域。接近障碍物标线的颜色，应根据障碍物所在的位置，与对向车行道分界线或同向车行道分界线的颜色一致。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  22 => 
  array (
    'id' => 5888,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面白色反光虚线警告前方路段要减速慢行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112455966.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '用于警告车辆驾驶人前方应减速慢行。收费广场减速标线设于收费广场及其前部适当位置，为白色反光虚线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  23 => 
  array (
    'id' => 5889,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面标记指示前方路口仅允许车辆向右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112456556.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '路面上的标志是禁止右转弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  24 => 
  array (
    'id' => 5890,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面标记指示前方路口禁止车辆掉头。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112454140.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '看路面上的标志一个X一个掉头的标志，合在一起就是禁止掉头的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  25 => 
  array (
    'id' => 5891,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧车道路面标线表示可以临时借公交专用车道行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112445428.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '公交专用车道是任何时候都不能占用的，属于专用车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  26 => 
  array (
    'id' => 5892,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面网状线标示不准进入该区域内停车等待。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112449799.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '路面黄色网状线表示严格禁止一切车辆长时或临时停车，防止交通阻塞。当黄色网状线前方有车辆停驶时，后车必须在黄色网状线外等候，直到确认黄色网状线前方有足够空间停驶本车时，方可驶过黄色网状线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  27 => 
  array (
    'id' => 5893,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面网状线标示允许进入该区域内等待。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112449796.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '路面黄色网状线表示严格禁止一切车辆长时或临时停车，防止交通阻塞。当黄色网状线前方有车辆停驶时，后车必须在黄色网状线外等候，直到确认黄色网状线前方有足够空间停驶本车时，方可驶过黄色网状线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  28 => 
  array (
    'id' => 5894,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路口内中心圈标示左小转弯要沿内侧行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112444836.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '左小转弯要沿内测行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  29 => 
  array (
    'id' => 5895,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路口两侧导流线表示直行或右转弯不得压线或越线行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112449010.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '导流线的形式主要为一个或几个根据路口地形设置的白色V形线或斜纹线区域，表示车辆必须规定的路线行驶，不得压线或越线行驶。主要用于过宽、不规则或行驶条件比较复杂的交叉路口，立体交叉的匝道口或其他特殊地点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  30 => 
  array (
    'id' => 5896,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方路口减速让行线表示要停车让干道车先行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112442364.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '是减速让行，不是停车让行!',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  31 => 
  array (
    'id' => 5897,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方路口停车让行线表示减速让干道车先行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112444511.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '应该是停车让干道车先行，而不是减速让行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  32 => 
  array (
    'id' => 5898,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路缘石的黄色实线指示路边允许临时停、放车辆。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112443967.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '黄色虚线是允许临时，全黄的就禁止了。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  33 => 
  array (
    'id' => 5899,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路缘石上的黄色虚线指示路边不允许停车上下人员或装卸货物。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112443083.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '应该是允许短时停车和卸货。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  34 => 
  array (
    'id' => 5900,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面同向车行道分界线指示不允许跨越超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112441944.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '实线不能跨越，故不能超越。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  35 => 
  array (
    'id' => 5901,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面同向车行道分界线指示允许跨越变换车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112441106.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '实线不能跨越，故不能变换车道超车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  36 => 
  array (
    'id' => 5902,
    'typeId' => '3',
    'type' => 0,
    'subject' => '黄色斜线填充线指示该区域禁止进入或压线行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112448564.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '黄色斜线填充双黄实线禁止跨越对向车行道分界线示例在路面较宽时，为保证车行道宽度不大于3.75m，双黄线间距可以适当调整。在双黄线间距大于50cm时应用黄色斜线或其他设施填充两条黄实线间的部分，禁止车辆压线或进入该区域。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  37 => 
  array (
    'id' => 5903,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中心黄色双实线指示可以暂时跨越超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112444802.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '中心黄色双实线表示严格禁止车辆跨线超车或压线行驶,属禁止标线。用以划分上下行方向各有两条或两条以上机动车道而没有设置中央分隔带的道路。除交叉路口或允许车辆左转变（或回转）路段外，均应连续设置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  38 => 
  array (
    'id' => 5904,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中心黄色虚实线指示允许暂时越过超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112444318.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '如果双黄线，一条是实线，一条是虚线，虚线在哪一侧，那侧的车辆就可以从此临时跨越，比如超车或转弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  39 => 
  array (
    'id' => 5905,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中心黄色实虚线指示允许超车时越过。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112435489.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '无论单黄线还是双黄线，只要是实线，就严禁跨越的，比如超车、转弯、掉头等。行车时没有特别情况就不应该越线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  40 => 
  array (
    'id' => 5906,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧路面标记表示可以暂时借用超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112433160.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '旁边是实线就不能越过，而且右侧是非机动车道，注意安全。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  41 => 
  array (
    'id' => 5907,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面标记指示这段道路上最低限速为60公里/小时。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112438825.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '白色的是最低车速，黄色的是最高车速。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  42 => 
  array (
    'id' => 5908,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面标记指示这段道路上最高限速为80公里/小时。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112439768.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '白色的是最低车速，黄色的是最高车速。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  43 => 
  array (
    'id' => 5909,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面标记指示这段道路上最高限速为50公里/小时。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112431064.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '限速标志为50公里/小时，所以此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  44 => 
  array (
    'id' => 5910,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头提示前方道路右侧有路口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112433785.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '导向箭头表示方向的意思，并不是右侧有路口的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  45 => 
  array (
    'id' => 5911,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头提示前方道路需向左合流。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112435306.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '此题无需解释，跟着箭头走就是向左合流。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  46 => 
  array (
    'id' => 5912,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方道路仅可左右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112435076.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '看路上的标志，是仅可以左转右转的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  47 => 
  array (
    'id' => 5913,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方路口仅能掉头。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112432740.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '导向箭头指示前方路口可以直行或者掉头，所以此题错误。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  48 => 
  array (
    'id' => 5914,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方路口可左转弯或掉头。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112433971.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '导向箭头指示前方路口可左转弯或掉头，此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  49 => 
  array (
    'id' => 5915,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方路口仅可左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112436902.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图片的标志为掉头的标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  50 => 
  array (
    'id' => 5916,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示在前方路口仅可右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112438522.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图中的标志表示可以直行或者右转弯。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  51 => 
  array (
    'id' => 5917,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示在前方路口仅可直行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112432921.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '该车道路面导向箭头指示在前方路口仅可直行或者左转。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  52 => 
  array (
    'id' => 5918,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方道路仅可左转。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112431748.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '看路面的导向箭头此车道为左转车道，是不能直行的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  53 => 
  array (
    'id' => 5919,
    'typeId' => '3',
    'type' => 0,
    'subject' => '该车道路面导向箭头指示前方道路仅可直行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112433770.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '该车道路面导向箭头指示前方道路仅可直行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  54 => 
  array (
    'id' => 5920,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路右侧白色矩形虚线框内表示允许长时间停车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112426888.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '虚线短时停车，实线长时停车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  55 => 
  array (
    'id' => 5921,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路右侧黄色矩形标线框内表示允许临时停车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112427514.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这么长的简易停车应该是公交车专用停车下客上车的，所以小车不能去停。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  56 => 
  array (
    'id' => 5922,
    'typeId' => '3',
    'type' => 0,
    'subject' => '允许沿着图中箭头方向驶入高速公路行车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112428649.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '不能跨越左边的白色实线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  57 => 
  array (
    'id' => 5923,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面上的出口标线用于引导驶出该高速公路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112428565.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '高速公路出入口标线为白色虚线，主要用于高速公路和其他采用立体交叉道路。该标线作用为，高速公路出入口横向标线或三角地带标线是为驶入或驶出匝道车辆提供安全交汇引导，减少与突出部缘石碰撞。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  58 => 
  array (
    'id' => 5924,
    'typeId' => '3',
    'type' => 0,
    'subject' => '高速公路两侧白色半圆状的间隔距离是50米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112429456.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '白色半圆状为车距确认线一般高速上出现的比较多，高速公路两侧车距确认线的间隔距离是50米。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  59 => 
  array (
    'id' => 5925,
    'typeId' => '3',
    'type' => 0,
    'subject' => '高速公路上的白色折线为行车中判断行车速度提供参考。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112428926.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '白色折线为车距确认线，是帮助您判断与前车的距离。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  60 => 
  array (
    'id' => 5926,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面上菱形标识预告前方道路设置人行横道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112424704.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '道路路面上的白色菱形图案，是中华人民共和国国家标准（GB5768/1999）《道路交通标志和标线》中新设置的一种交通标线，正式名称为人行横道预告标线。这种标线在到达人行横道前的道路30-50米处设置，它的作用是用来提示驾驶员，前方已接近人行横道，应减速慢行，并须注意行人横过马路。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  61 => 
  array (
    'id' => 5927,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路口内人行横道线警示行人优先横过道路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112422445.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '请记住，无论任何时候都是行人优先。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  62 => 
  array (
    'id' => 5928,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面可变导向车道线指示可以随意选择通行方向。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112423006.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '可变车道，是指车道内侧划了多条斜线，有点像趴下的“非”字，顾名思义就是能随时根据交通流量更改指示方向的车道。这个主要是针对部分高峰时段车流集中，但车道偏少；或者早晚高峰时段来回车流量有明显差异的路段。碰到这种车道，根据可变车道的指示走就行了。是有时段限制的，不是任何时候都可以随意选择通行方向。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  63 => 
  array (
    'id' => 5929,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如下图，进入交叉口前，车辆不允许跨越白色实线变更车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112424062.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '白色实线不能压，此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  64 => 
  array (
    'id' => 5930,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如下图，路口导向线用于辅助车辆转弯行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112421444.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '此题几乎可以不用看图，路口导向线就是用于辅助车辆转弯行驶的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  65 => 
  array (
    'id' => 5931,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如下图，路口导向线用于辅助车辆转弯行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112427065.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '看到路中间的白色虚线没，为路口导向线用于辅助车辆转弯行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  66 => 
  array (
    'id' => 5932,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如下图，左转弯车辆不可以直接进入左转弯待转区，等待放行信号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112425819.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '“左转弯待转区”只供左转弯车辆使用，在同向直行绿灯亮时，虽然左转弯灯是红色，但此时允许左转弯的车辆进入路口“左转弯待转区”等候，等到左转弯信号灯变成绿色时再通过路口。若直行道和左转弯信号灯信号灯都为红色时，所有车辆需再停止线等待。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  67 => 
  array (
    'id' => 5933,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如下图，左转弯车辆可直接进入左转弯待转区，等待放行信号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112423330.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '直行的信号灯是红色是禁止进入左转待转区域的。如果直行信号灯为绿色左转信号灯为红色则可以进入“左转待转区”等候。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  68 => 
  array (
    'id' => 5934,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面白色虚线实线指示实线一侧允许跨越。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112425901.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '虚线一侧可以越线，您所在的这个车道是不可以的，因为是实线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  69 => 
  array (
    'id' => 5935,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面白色虚实线指示变道或靠边停车时允许跨越。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112413581.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '车行道边缘白色虚实线的虚线侧允许车辆越线行驶，实线侧不允许车辆越线行驶，用以规范车辆行驶轨迹。在必要的地点，如公交车站临近路段、允许路边停车路段等，可设置车行道边缘白色虚实线。跨线行驶的车辆，应避让其他正常行驶的车辆、非机动车辆和行人。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  70 => 
  array (
    'id' => 5936,
    'typeId' => '3',
    'type' => 0,
    'subject' => '道路右侧车行道边缘白色虚线指示允许跨越。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112414678.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '如果是需要通过路口右转弯，是允许被跨越的，但是如果直行就不允许越线占非机动车道行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  71 => 
  array (
    'id' => 5937,
    'typeId' => '3',
    'type' => 0,
    'subject' => '道路右侧白色实线标示机动车道与人行道的分界线。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112413634.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这是机动车与非机动车的分界线，看旁边有非机动车标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  72 => 
  array (
    'id' => 5938,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中两条双黄虚线并列组成的双黄虚线指示潮汐车道的位置。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112412607.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '所谓“潮汐车道”，是指根据早晚交通流量不同情况，对有条件的道路，通过车道灯的指示方向变化，控制主干道车道行驶方向，来调整车道数。例如，左转车道很拥堵，但直行车道却车少畅通；交警通过遥控装置，在很短的时间，直行车道就变为左转车道，左转车龙就“消化”了，车道可随车流量随时变化。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  73 => 
  array (
    'id' => 5939,
    'typeId' => '3',
    'type' => 0,
    'subject' => '道路右侧白色虚线指示可越线变更车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112414650.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '试题说的是可越线变更车道并没说右侧超车，白色虚线是可以变更车道的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  74 => 
  array (
    'id' => 5940,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中黄色虚线指示任何情况都不允许越线绕行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112418863.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '黄色虚线是中心线，可以跨越；黄色单实线就已经不能跨越了；黄色双实线是严格禁止压线或者跨越。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  75 => 
  array (
    'id' => 5941,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路面中心黄虚线指示在保证安全的情况下可以越线超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112416382.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '黄色虚线是中心线，可以跨越；黄色单实线就已经不能跨越了；黄色双实线是严格禁止压线或者跨越。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  76 => 
  array (
    'id' => 5942,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志预告距离高速公路东芦山服务区2公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112417591.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '此图为服务区，没有问题。顺便普及下服务区跟停车区的区别：服务区有吃住加油等服务，停车区属于紧急停车，没有相关的服务。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  77 => 
  array (
    'id' => 5943,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提示前方200米是车距确认路段。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112419428.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '路面上的白色折线是车距确认线，此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  78 => 
  array (
    'id' => 5944,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志指示路右侧是高速公路临时停车处。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112415155.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图示为紧急停车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  79 => 
  array (
    'id' => 5945,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示距离前方加油站入口200米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112418479.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '绿色标志已经标注清楚，前方200米加油站。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  80 => 
  array (
    'id' => 5946,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示高速公路紧急电话的位置。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112415386.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此标志的意思是紧急电话号码，112救援。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  81 => 
  array (
    'id' => 5947,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示距离设有电子不停车收费车道的收费站1公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112413571.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => 'ETC,使用该系统，车主只要在车窗上安装感应卡并预存费用，通过收费站时便不用人工缴费，也无须停车，高速费将从卡中自动扣除。虽然能实现不停车收费，但一般来说，车辆还是需要以较低速度通过。这种收费系统每车收费耗时不到两秒，其收费通道的通行能力是人工收费通道的5到10倍。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  82 => 
  array (
    'id' => 5948,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示前方收费站设有电子不停车收费行驶车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112412836.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => 'ETC车道就是电子不停车收费行驶车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  83 => 
  array (
    'id' => 5949,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提示距离设有电子不停车收费车道的收费站1公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112402332.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '标志的意思是人工收费站，不是电子不停车收费车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  84 => 
  array (
    'id' => 5950,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志提示前方收费口停车领卡。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112403023.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '这个没问题，图片显示的很清楚，有个手拿卡，简直就是看图说话。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  85 => 
  array (
    'id' => 5951,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志指示高速公路交通广播和无线电视频道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112404236.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '不是雷达测速。是“道路交通信息”，指示收听高速公路或城市快速路交通信息广播的频率。此是错在“无线电视频道”！！',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  86 => 
  array (
    'id' => 5952,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告前方距高速公路终点还有2公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112406686.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '有条紅线的高速公路预告牌就是指这条标号某某号的高速公路到了终点，没有斜紅线就是指高速公路的起点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  87 => 
  array (
    'id' => 5953,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示前方是高速公路的终点。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112402847.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这是起点标志，终点的是还有一个红色斜杠的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  88 => 
  array (
    'id' => 5954,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告距离下一左侧出口1公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112403033.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '应该是距离右侧下一个出口，还有一公里。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  89 => 
  array (
    'id' => 5955,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告距离下一出口4公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112406078.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '标志没问题，此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  90 => 
  array (
    'id' => 5956,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志指示高速公路的名称和编号。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112407988.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '国家高速用字母“G”表示，省级的有“S”表示，国家高速公路命名标志由“国家高速”，“编号名称”和“高速名称”三部分组成。国家高速为红底白字，省级为黄底黑字。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  91 => 
  array (
    'id' => 5957,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告高速公路终点距离信息。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112402431.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '标志的意思是告之道路前方要经过的重要的城镇的地名和距离。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  92 => 
  array (
    'id' => 5958,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志指示高速公路两个行驶方向的目的地。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112403447.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '指示高速公路两个行驶方向的目的地。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  93 => 
  array (
    'id' => 5959,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告高速公路入口在路右侧。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112408254.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此为入口预告标志，没有预告高速公路入口在路右侧的标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  94 => 
  array (
    'id' => 5960,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告距离高速公路入口1公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112402522.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '如图所示，右侧标志为预告高速入口距离标志，表示前方1公里处为高速公里路口。因此本题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  95 => 
  array (
    'id' => 5961,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志警示前方道路右侧不能通行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112406989.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '如图所示，左侧标志为右侧通行标志。左侧不能通行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  96 => 
  array (
    'id' => 5962,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警示前方道路两侧不能通行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112406797.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这是两侧通行标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  97 => 
  array (
    'id' => 5963,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路两侧的标志提示前方道路线形变化。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112405421.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '如图所示，道路两侧标志为线性诱导标志，提示前方道路线性变化。此标志的作用就是及时地提醒驾驶人员前方道路线形和道路状况的变化，在到达危险点以前有充分时间采取必要行动，确保行驶安全。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  98 => 
  array (
    'id' => 5964,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志提示该路段已实行交通监控。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112408570.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '交通监控设备标志属于指路标志，蓝底、白图形、白边框、蓝色衬边。用于告知机动车驾驶人该区域设置有固定式交通监控设备。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  99 => 
  array (
    'id' => 5965,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方是分流路口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112408928.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图中是车道数增加标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  100 => 
  array (
    'id' => 5966,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志表示前方车道数量增加。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112402113.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '图中是车道数变少标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  101 => 
  array (
    'id' => 5967,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示前方是T型路口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112395734.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '应该是此路不通。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  102 => 
  array (
    'id' => 5968,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右前方标志指示前方路口左转弯绕行的路线。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112393913.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意题目，问的是右侧标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  103 => 
  array (
    'id' => 5969,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示前方路口绕行的路线。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112395155.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意题目，问的是右侧标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  104 => 
  array (
    'id' => 5970,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志告知右前方100米是应急避难场所。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112399928.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '此标志适用于突发公共事件状态下，供居民紧急疏散、安置的应急避难场所的标志设置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  105 => 
  array (
    'id' => 5971,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志指示前方设有避让来车的处所。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112393850.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '这个不常见，旁边没有公交站台，看见这题选对就好！',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  106 => 
  array (
    'id' => 5972,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志告知向右100米为室内停车场。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112386832.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => 'P字上边还有个遮挡，说明是室内的，如果没有上边那个遮挡就是露天停车场。下面白色辅助标志说明了前方100米的距离是停车场。本题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  107 => 
  array (
    'id' => 5973,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志告知前方200米处是露天停车场。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112386074.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '单纯一个P字就是露天停车场，如果P字上边还有个遮挡，就是室内的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  108 => 
  array (
    'id' => 5974,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志指示前方所要经过的重要地名和距离。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112381272.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '问的是前方的标志，不是地上的网格线。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  109 => 
  array (
    'id' => 5975,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告互通式立交桥通往方向的信息。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112387925.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '互通式立交桥是城市里最常见的立交桥。互通式立交桥桥上面的道路与桥下面的道路有道路(即匝道)相联结，使上下道路上的车辆可以通行。互通式立交桥包括完全互通和部分互通两种形式，根据其平面上的形状，又有苜蓿叶形、菱形、环形、喇叭形立交桥等类型。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  110 => 
  array (
    'id' => 5976,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志告知各个路口出口方向的信息。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112384109.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '前方标志告知各个路口出口方向的信息，此题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  111 => 
  array (
    'id' => 5977,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志告知前方道路各行其道的信息。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112388593.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '不是各行其道，是各方向终点信息。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  112 => 
  array (
    'id' => 5978,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志预告交叉路口通往方向的信息。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112385676.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '此为交叉路口预告标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  113 => 
  array (
    'id' => 5979,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志表示此处允许机动车掉头。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112383615.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '黄色虚线+掉头标志都表明此处是可以掉头的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  114 => 
  array (
    'id' => 5980,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志表示除大客车以外的其他车辆不准进入右侧车道行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112375024.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '注意前方绿灯旁图示为公交车，应该是除公交车外。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  115 => 
  array (
    'id' => 5981,
    'typeId' => '3',
    'type' => 0,
    'subject' => '红色圆圈内标志表示除非机动车以外的其他车辆不准进入该车道行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112375118.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '红色圈内是非机动车专用车道标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  116 => 
  array (
    'id' => 5982,
    'typeId' => '3',
    'type' => 0,
    'subject' => '红色圆圈内标志表示左侧道路只供小型车行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112373172.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '红色圆圈内标志是机动车行驶车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  117 => 
  array (
    'id' => 5983,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志表示除公交车以外的其他车辆不准进入该车道行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112378076.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '公交专用车道禁止其他车辆行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  118 => 
  array (
    'id' => 5984,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示车辆按箭头示意方向选择行驶车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112375359.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '图示为分向行驶车道标志。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  119 => 
  array (
    'id' => 5985,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示会车时对向车辆先行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112371555.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此图是会车先行的意思，谁粗谁先行。明显白色箭头比较粗，所以我们先行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  120 => 
  array (
    'id' => 5986,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示干路车辆优先通行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112375292.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '干道的意思应该是主干道的意思，主干道的箭头比较粗当然先行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  121 => 
  array (
    'id' => 5987,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示最高车速不准超过每小时50公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112376966.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此图的标志为最低车速不低于50公里/小时。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  122 => 
  array (
    'id' => 5988,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示鸣喇叭提醒。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112375395.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '右侧标志是人行横道的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  123 => 
  array (
    'id' => 5989,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示此处不准鸣喇叭。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112378311.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '右侧标志是表示此处要鸣喇叭的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  124 => 
  array (
    'id' => 5990,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右前方标志表示该路段在规定时间内只供步行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112371065.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '如图所示，右侧标志为步行指示标志，该标志组合表示在指定时间段只能步行。因此本题正确。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  125 => 
  array (
    'id' => 5991,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示注意避让直行方向来的机动车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112378439.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '单行路标志一般位于路的右侧，以立牌显示，与直行线标志（圆形）不同的是，它是长方框的。直行：表示只准一切车辆直行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  126 => 
  array (
    'id' => 5992,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志表示向右是单向行驶道路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112379914.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '单行路向右：表示一切车辆向左或向右单向行驶。此标志设在单行路的路口和入口处的适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  127 => 
  array (
    'id' => 5993,
    'typeId' => '3',
    'type' => 0,
    'subject' => '前方标志表示向左是单向行驶道路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112374291.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '单行路向左：表示一切车辆向左或向右单向行驶。此标志设在单行路的路口和入口处的适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  128 => 
  array (
    'id' => 5994,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志表示前方立体交叉处可以直行和右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112372024.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '立体交叉直行和左转弯行驶：表示一切车辆在立体交叉处可以直行和按图示路线左转弯行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  129 => 
  array (
    'id' => 5995,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示只能靠左侧道路行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112367152.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '靠左侧道路行驶：表示只准一切车辆靠左侧道路行驶。此标志设在车辆必须靠左侧行驶的路口以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  130 => 
  array (
    'id' => 5996,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示只能车辆向右转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112361196.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '向右转弯：表示只准一切车辆向右转弯。此标志设在车辆必须向右转弯的路口以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  131 => 
  array (
    'id' => 5997,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路口只能车辆向左转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112368902.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '向左转弯：表示只准一切车辆向左转弯。此标志设在车辆必须向左转弯的路口以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  132 => 
  array (
    'id' => 5998,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路口7:30-10:00允许车辆直行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112366200.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '直行：表示只准一切车辆直行。此标志设在直行的路口以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  133 => 
  array (
    'id' => 5999,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方100米是停车接受检查的地点。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112362262.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '停车检查：表示机动车必须停车接受检查。此标志设在关卡将近处，以便要求车辆接受检查或缴费等手续。标志中可加注说明检查事项。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  134 => 
  array (
    'id' => 6000,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路段解除时速40公里限制。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112362024.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '解除限制速度：表示限制速度路段结束。此标志设在限制车辆速度路段的终点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  135 => 
  array (
    'id' => 6001,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路段解除时速40公里限制。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112361050.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '限制速度：表示该标志至前方限制速度标志的路段内，机动车行驶速度不得超过标志所示数值。此标志设在需要限制车辆速度的路段的起点。以图为例：限制行驶时速不得超过40公里。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  136 => 
  array (
    'id' => 6002,
    'typeId' => '3',
    'type' => 0,
    'subject' => '隧道上方标志表示限制高度3.5米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112367124.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '表示禁止装载高度超过标志所示数值的车辆通行。此标志设在最大允许高度受限制的地方。以图为例：装载高度不得超过3.5米。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  137 => 
  array (
    'id' => 6003,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方道路限宽3米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112366425.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '限制宽度：表示禁止装载宽度超过标志所示数值的车辆通行。此标志设在最大允许宽度受限制的地方。以图为例：装载宽度不得超过3米。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  138 => 
  array (
    'id' => 6004,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示不允许长时鸣喇叭。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112363677.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '禁止鸣喇叭：表示禁止鸣喇叭。此标志设在需要禁止鸣喇叭的地方。禁止鸣喇叭的时间和范围可用辅助标志说明。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  139 => 
  array (
    'id' => 6005,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示临时停车不受限制。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112369141.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '禁止车辆临时或长时停放：表示在限定的范围内，禁止一切车辆临时或长时停放。此标志设在禁止车辆停放的地方。禁止车辆停放的时间、车种和范围可用辅助标志说明。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  140 => 
  array (
    'id' => 6006,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志表示前方路段不允许超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112362827.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '解除禁止超车：表示禁止超车路段结束。此标志设在禁止超车的终点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  141 => 
  array (
    'id' => 6007,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路段允许超车。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112369797.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '禁止超车：表示该标志至前方解除禁止超车标志的路段内，不准机动车超车。此标志设在禁止超车的起点。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  142 => 
  array (
    'id' => 6008,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志表示前方路口不准掉头。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112361938.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '禁止掉头：表示前方路口禁止一切车辆掉头。此标志设在禁止掉头的路口前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  143 => 
  array (
    'id' => 6009,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路口不准车辆右转。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112363083.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '禁止向右转弯：表示前方路口禁止一切车辆向右转弯。此标志设在禁止向右转弯的路口前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  144 => 
  array (
    'id' => 6010,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路口不准车辆左转。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112364841.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '禁止向左转弯：表示前方路口禁止一切车辆向左转弯。此标志设在禁止向左转弯的路口前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  145 => 
  array (
    'id' => 6011,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提示一切车辆都不能驶入。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112367034.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '禁止驶入：表示禁止车辆驶入。此标志设在禁止驶入的路段入口处。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  146 => 
  array (
    'id' => 6012,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志表示前方路段允许进入。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112356088.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '禁止通行：表示禁止一切车辆和行人通行。此标志设在禁止通行的道路入口处。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  147 => 
  array (
    'id' => 6013,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路段会车时停车让对方车先行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112352279.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '减速让行：表示车辆应减速让行，告示车辆驾驶员必须慢行或停车，观察干路行车情况，在确保干道车辆优先的前提下，认为安全时方可续行。此标志设在视线良好交叉道路的次要路口。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  148 => 
  array (
    'id' => 6014,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志表示前方路口要停车让行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112352197.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '停车让行：表示车辆必须在停止线以外停车了望，确认安全后，才准许通行。停车让行标志在下列情况下设置：(1)与交通流量较大的干路平交的支路路口；(2)无人看守的铁路道口；(3)其他需要设置的地方。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  149 => 
  array (
    'id' => 6015,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方右侧500米有避险车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112356292.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '避险车道是指在长陡下坡路段行车道外侧，增设的供速度失控车辆，驶离正线安全减速的专用车道。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  150 => 
  array (
    'id' => 6016,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方注意右侧路口有汇入车辆。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112352907.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意合流，用以警告车辆驾驶人注意前方有车辆汇合进来。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  151 => 
  array (
    'id' => 6017,
    'typeId' => '3',
    'type' => 0,
    'subject' => '左侧标志警告前方注意左侧路口有汇入车辆。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112353638.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意合流，用以警告车辆驾驶人注意前方有车辆汇合进来。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  152 => 
  array (
    'id' => 6018,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志警告进入隧道减速慢行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112356425.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这个标志是隧道开灯的意思。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  153 => 
  array (
    'id' => 6020,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如图所示，右侧标志提醒前方道路正在施工。',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210529200232/201511231112351678.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '施工标志：用以告示前方道路施工，车辆应减速慢行或绕道行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  154 => 
  array (
    'id' => 6021,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方路段有塌方禁止通行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112358566.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '注意危险：用以促使车辆驾驶员谨慎慢行。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  155 => 
  array (
    'id' => 6022,
    'typeId' => '3',
    'type' => 0,
    'subject' => '图中标志提醒障碍物在路中，车辆从左侧绕行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112356558.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '右侧绕行：用以告示前方道路有障碍物，车辆应按标志指示减速慢行，右侧绕行通过。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  156 => 
  array (
    'id' => 6023,
    'typeId' => '3',
    'type' => 0,
    'subject' => '图中标志提醒障碍物在路中，车辆从右侧绕行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112352998.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '左侧绕行：用以告示前方道路有障碍物，车辆应按标志指示减速慢行，左侧绕行通过。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  157 => 
  array (
    'id' => 6024,
    'typeId' => '3',
    'type' => 0,
    'subject' => '图中标志提醒障碍物在路中，车辆从两侧绕行。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112357181.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '左右绕行：用以告示前方道路有障碍物，车辆应按标志指示减速慢行，绕行通过。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  158 => 
  array (
    'id' => 6025,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方路段保持车距。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112354968.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '事故易发路段：此标志设在交通事故易发路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  159 => 
  array (
    'id' => 6026,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志告知前方注意残疾人。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112355127.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '黄色三角形的都是警告标志，提醒驾驶人要注意。学校是辅助标志，也是提醒驾驶人注意。此题前方注意残疾人是对的，前方注意学校也是对的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  160 => 
  array (
    'id' => 6027,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方是非机动车道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112353259.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '黄底的是注意的意思，此标志的意思是注意非机动车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  161 => 
  array (
    'id' => 6028,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方150米是无人看守铁路道口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112351697.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '有火车头是无人看守，无火车头是有人看守。横着一杠50、两杠100、三杠150。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  162 => 
  array (
    'id' => 6029,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告距前方有人看守铁路道口150米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112343251.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此标志为“无人看守的铁路道口”，火车下面的红线标志，一道是50米，两道红线是100米，三道红线是150米。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  163 => 
  array (
    'id' => 6030,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告距前方有人看守铁路道口100米。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112349162.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '此标志为前方50米处无人看守铁道路口。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  164 => 
  array (
    'id' => 6031,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这个标志警告前方铁路道口有多股铁路与道路相交。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112342166.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '上面带叉和折线就代表不止一条铁路！',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  165 => 
  array (
    'id' => 6032,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志警告前方是有人看守铁路道口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112348009.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '记住，有车无人看守，无车有人看守。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  166 => 
  array (
    'id' => 6033,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志警告前方是无人看守的有多股铁路与道路相交铁路道口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112349321.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '这是有人看守铁道路口。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  167 => 
  array (
    'id' => 6034,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方是过水路面。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112343195.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '过水路面指的是：通过平时无水或流水很少的宽浅河流，而修筑的在洪水期间容许水流浸过的路面。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  168 => 
  array (
    'id' => 6035,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒前方路面低洼。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112343375.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '路面低洼：用以提醒车辆驾驶人减速慢行。设在路面突然低洼以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  169 => 
  array (
    'id' => 6036,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒注前方是驼峰桥。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112348397.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '实心的：路面高突标志；空心的：驼峰桥。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  170 => 
  array (
    'id' => 6037,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方路面不平。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112346584.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '路面不平：用以提醒车辆驾驶人减速慢行。设在路面颠簸路段或桥头跳车较严重的地点以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  171 => 
  array (
    'id' => 6038,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方路面高突。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112343258.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '驼峰桥：用以提醒车辆驾驶人谨慎驾驶。设在拱度很大，影响视距的驼峰桥以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  172 => 
  array (
    'id' => 6039,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方路交口向右100米是渡口。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112346044.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '渡口：用以提醒车辆驾驶人谨慎驾驶。设在车辆渡口以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  173 => 
  array (
    'id' => 6040,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒前方是单向行驶隧道。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112347642.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '隧道：用以提醒车辆驾驶人注意慢行。设在双向行驶并且照明不好的隧道口前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  174 => 
  array (
    'id' => 6041,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方200米有村庄。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112339947.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '上边是村庄：用以提醒车辆驾驶人小心驾驶。设在紧靠村庄、集镇且视线不良的路段以前适当位置。下边200米是距离。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  175 => 
  array (
    'id' => 6042,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒注意前方200米是堤坝道路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112332881.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '前方200米是堤坝路：用以提醒车辆驾驶人小心驾驶，设在沿水库、湖泊、河流等堤坝道路以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  176 => 
  array (
    'id' => 6043,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒注意前方是傍山险路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112334828.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '傍山险路：用以提醒车辆驾驶人小心驾驶。设在傍山险路路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  177 => 
  array (
    'id' => 6044,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提示前方是连续急转弯道路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112336026.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '易滑：用以促使车辆驾驶人注意慢行。设在路滑容易发生事故的路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  178 => 
  array (
    'id' => 6045,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒前方山口注意横风。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112332816.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意横风：用以提醒车辆驾驶人小心驾驶。设在经常有很强的侧向风的路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  179 => 
  array (
    'id' => 6046,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧这个标志提醒注意左侧有落石危险。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112339096.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意落石：用以提醒车辆驾驶人注意落石。设在有落石危险的傍山路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  180 => 
  array (
    'id' => 6047,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方是左侧傍山险路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112337883.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '注意落石：用以提醒车辆驾驶人注意落石。设在有落石危险的傍山路段以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  181 => 
  array (
    'id' => 6048,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方路段设有信号灯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112338330.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '注意信号灯：用以警告车辆驾驶人注意前方路段设有信号灯，应依信号灯指示行车。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  182 => 
  array (
    'id' => 6049,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方路段要注意儿童。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/41b561cf6f53c802.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '不要被标志下边的学校误导，题目问的是标志的意思，注意儿童：用以警告车辆驾驶人减速慢行，注意儿童。设在小学、幼儿园、少年宫等儿童经常出入地点前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  183 => 
  array (
    'id' => 6050,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方经常有牲畜横穿、出入。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112336434.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '牛的标志是：注意牲畜；小鹿的标志是：注意保护野生动物。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  184 => 
  array (
    'id' => 6051,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志提醒前方是野生动物保护区。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112339866.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '注意牲畜：用以提醒车辆驾驶人注意慢行。设在经过放牧区、畜牧场等区域的公路上，经常有牲畜横穿、出入的地点前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  185 => 
  array (
    'id' => 6052,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方路段要注意儿童。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112335835.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '注意行人：用以警告车辆驾驶人减速慢行，注意行人。设在行人密集，或不易被驾驶员发现的人行横道线以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  186 => 
  array (
    'id' => 6053,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方进入两侧变窄路段。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112333196.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '窄桥：用以警告车辆驾驶人注意前方桥面宽度变窄，应谨慎驾驶。设在桥面净宽较两端路面宽度变窄，且桥的净宽小于6m的桥梁以前适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  187 => 
  array (
    'id' => 6054,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方道路左侧变宽。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112331926.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '左侧变窄：用以警告车辆驾驶人注意前方车行道或路面狭窄情况，遇有来车应予减速避让。设在双车道路面宽度缩减为6m以下的路段起点前方。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  188 => 
  array (
    'id' => 6055,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方道路右侧变宽。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112339933.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '右侧变窄：用以警告车辆驾驶人注意前方车行道或路面狭窄情况，遇有来车应予减速避让。设在双车道路面宽度缩减为6m以下的路段起点前方。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  189 => 
  array (
    'id' => 6056,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方路面两侧变窄长度为5公里。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112333322.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '两侧变窄：用以警告车辆驾驶人注意前方车行道或路面狭窄情况，遇有来车应予减速避让。设在双车道路面宽度缩减为6m以下的路段起点前方。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  190 => 
  array (
    'id' => 6057,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方是下陡坡路段。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112339020.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '连续下坡：用以提醒车辆驾驶人小心驾驶。当连续下坡总长大于3km后，应重复设置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  191 => 
  array (
    'id' => 6058,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方是连续下坡路段。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112337104.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '下陡坡：用以提醒车辆驾驶人小心驾驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  192 => 
  array (
    'id' => 6059,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方是上陡坡路段。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112332740.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '上陡坡：用以提醒车辆驾驶人小心驾驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  193 => 
  array (
    'id' => 6060,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方是向右反向弯路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112331073.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '连续弯路：用以警告车辆驾驶人减速慢行。设置位置为连续弯路起点的外面，当连续弯路总长度大于500m时，应重复设置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  194 => 
  array (
    'id' => 6061,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方道路是向左连续弯路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112329974.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '向左急弯路：用以警告车辆驾驶人减速慢行。设置位置为曲线起点的外面，但不应进入相邻的圆曲线内。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  195 => 
  array (
    'id' => 6062,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警示前方道路有连续三个或三个以上的弯路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112321999.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '反向弯路：用以警告车辆驾驶人减速慢行。反向弯路交通警告标志多处于山路，是一个事故多发点，极其危险，开车上下山一定要慢。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  196 => 
  array (
    'id' => 6063,
    'typeId' => '3',
    'type' => 0,
    'subject' => '右侧标志警告前方是向右急转弯路。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112325444.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '向右急弯路：用以警告车辆驾驶人减速慢行。设置位置为曲线起点的外面，但不应进入相邻的圆曲线内。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  197 => 
  array (
    'id' => 6064,
    'typeId' => '3',
    'type' => 1,
    'subject' => '右侧标志提示哪种车型不能通行？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112323869.jpg',
    'optionA' => '大型货车',
    'optionB' => '大型客车',
    'optionC' => '各种机动车',
    'optionD' => '小型客货车',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '禁止机动车通行：表示禁止某种机动车通行。此标志设在禁止机动车通行的路段入口处。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  198 => 
  array (
    'id' => 6065,
    'typeId' => '3',
    'type' => 1,
    'subject' => '右侧标志表示什么？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112322966.jpg',
    'optionA' => '前方道路靠右侧行驶',
    'optionB' => '前方道路不允许直行',
    'optionC' => '前方是直行单行路',
    'optionD' => '前方注意右侧路口',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '表示一切车辆单向行驶。此标志设在单行路的路口和入口处的适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  199 => 
  array (
    'id' => 6066,
    'typeId' => '3',
    'type' => 1,
    'subject' => '立体交叉处这个标志提示什么？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112327142.jpg',
    'optionA' => '向右转弯',
    'optionB' => '直行和左转弯',
    'optionC' => '直行和右转弯',
    'optionD' => '在桥下掉头',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '立交直行和右转弯行驶：表示车辆在立交处可以直行和按图示路线右转弯行驶。此标志设在立交右转弯出口处适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  200 => 
  array (
    'id' => 6067,
    'typeId' => '3',
    'type' => 1,
    'subject' => '立体交叉处这个标志提示什么？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112323700.jpg',
    'optionA' => '向右转弯',
    'optionB' => '直行和左转弯',
    'optionC' => '直行和右转弯',
    'optionD' => '在桥下掉头',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '立交直行和左转弯行驶-表示车辆在立交处可以直行和按图示路线左转弯行驶。此标志设在立交左转弯出口处适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  201 => 
  array (
    'id' => 6068,
    'typeId' => '3',
    'type' => 1,
    'subject' => '右侧标志是何含义？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112322992.jpg',
    'optionA' => '允许长时停放车辆',
    'optionB' => '可以临时停车',
    'optionC' => '允许长时停车等客',
    'optionD' => '不允许停放车辆',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '禁止车辆长时停放：禁止车辆长时停放，临时停放不受限制。禁止车辆停放的时间、车种和范围可用辅助标志说明。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  202 => 
  array (
    'id' => 6069,
    'typeId' => '3',
    'type' => 1,
    'subject' => '右侧标志是何含义？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112327401.jpg',
    'optionA' => '不允许停放车辆',
    'optionB' => '允许临时停车',
    'optionC' => '允许停车上下客',
    'optionD' => '允许停车装卸货',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '表示在限定的范围内，禁止一切车辆临时或长时停放。此标志设在禁止车辆停放的地方。禁止车辆停放的时间、车种和范围可用辅助标志说明。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  203 => 
  array (
    'id' => 6070,
    'typeId' => '3',
    'type' => 1,
    'subject' => '右侧标志警示前方是什么路口？',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112322564.jpg',
    'optionA' => 'T型交叉路口',
    'optionB' => 'Y型交叉路口',
    'optionC' => '十字交叉路口',
    'optionD' => '环行交叉路口',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '交叉路口-用以警告车辆驾驶人谨慎慢行，注意横向来车。设在平面交叉路口驶入路段的适当位置。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  204 => 
  array (
    'id' => 6074,
    'typeId' => '3',
    'type' => 0,
    'subject' => '这辆红色轿车可以在该车道行驶。',
    'subjectSimple' => NULL,
    'imgUrl' => 'https://www.jiazhao.com/images/tiku/201511231112318796.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '红色轿车行驶的车道的信号灯是红色，所以是不能在该道行驶的。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  205 => 
  array (
    'id' => 7592,
    'typeId' => '3',
    'type' => 0,
    'subject' => '路中心的双黄实线作用是分隔对向交通流，在保证安全的前提下，可越线超车或转弯。',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210524000222/odNs30ZttDW.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '双黄实线禁止跨越对向车行道分界线：道路中心为双黄实线，用于分隔对向行驶的交通流，并禁止双方向车辆越线或压线行驶。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  206 => 
  array (
    'id' => 7995,
    'typeId' => '3',
    'type' => 2,
    'subject' => '如图所示,驾驶机动车该标志,以下说法正确的是什么?',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210602153748/t015811f5895255eb2c.jpg',
    'optionA' => '该标志表示禁止超车',
    'optionB' => '该标志表示禁止超车路段结束',
    'optionC' => '该标志通常遇禁止超车标志成对使用',
    'optionD' => '该标志设在禁止超车路段的终点',
    'answer' => '2,4',
    'optionSimple' => NULL,
    'solve' => '解除禁止超车标志表示禁止超车路段结束。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  207 => 
  array (
    'id' => 8013,
    'typeId' => '3',
    'type' => 0,
    'subject' => '如图所示，右侧标志提醒前方有村庄或集镇建议行驶速度30公里每小时',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210603223122/26d5ace2153ec0d7.jpg',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '警告标志是村庄或集镇，下面是建议速度标志。提醒前方有村庄或集镇，建议速度30公里/小时。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  208 => 
  array (
    'id' => 8053,
    'typeId' => '3',
    'type' => 1,
    'subject' => '如图所示，该机动车行驶至该路口，以下说法正确的是什么？',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210604225544/325.jpg',
    'optionA' => '此时不可以直接进入左转弯待转区',
    'optionB' => '此时可以直接进入左转弯待转区等候放',
    'optionC' => '应在直行绿灯亮起后再进入左转弯待转',
    'optionD' => '左转灯亮起后才可进入左转弯待转区',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  209 => 
  array (
    'id' => 8054,
    'typeId' => '3',
    'type' => 1,
    'subject' => '如图所示，该机动车行驶至该路口，以下说法正确的是什么？',
    'subjectSimple' => NULL,
    'imgUrl' => 'http://qinhu-association-album.oss-cn-hangzhou.aliyuncs.com/admin/20210604225922/image-699.jpg',
    'optionA' => '此时不可以直接进入左转弯待转区',
    'optionB' => '此时可以直接进入左转弯待转区等候放',
    'optionC' => '应在直行绿灯亮起后再进入左转弯待转',
    'optionD' => '左转灯亮起后才可进入左转弯待转区',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '“左转弯待转区”只供左转弯车辆使用，在同向直行绿灯亮时，虽然左转弯灯是红色，但此时允许左转弯的车辆进入路口“左转弯待转区”等候，等到左转弯信号灯变成绿色时再通过路口。若直行道和左转弯信号灯都为红色时，所有车辆需在停止线等待。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
);
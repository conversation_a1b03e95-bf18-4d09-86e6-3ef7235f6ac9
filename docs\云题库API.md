# 题库搜索API文档

## 基础信息

- **基础URL**: `http://tiku.uzdns.com/ api`
- **返回格式**: JSON
- **字符编码**: UTF-8
- **鉴权方式**: API密钥
- **请求方法**: 仅支持POST方法
- **请求记录**: 所有API请求都会被记录用于监控和分析

## 鉴权说明

所有API接口都需要提供有效的API密钥进行鉴权。API密钥只能通过HTTP Header方式传递：

### HTTP Header方式 (推荐)
```
POST /api/tiku/search
X-API-Key: tk_your_api_key_here
Content-Type: application/json
```

### Authorization Bearer方式
```
POST /api/tiku/search
Authorization: Bearer tk_your_api_key_here
Content-Type: application/json
```

**注意**: 不再支持查询参数方式传递API密钥，所有请求必须使用POST方法。

### 鉴权错误响应

| 状态码 | 错误信息 | 说明 |
|--------|----------|------|
| 401 | 缺少API密钥 | 请求中未提供API密钥 |
| 401 | API密钥无效 | 提供的API密钥不存在 |
| 401 | API密钥验证失败 | API密钥格式错误或已被篡改 |
| 401 | API密钥已被禁用 | API密钥状态为禁用 |
| 401 | API密钥已过期 | API密钥已超过有效期 |
| 403 | 权限不足 | API密钥没有访问该接口的权限 |
| 429 | 请求频率超限 | 超过了API密钥的速率限制 |

### 权限说明

API密钥支持细粒度权限控制：

- `tiku.search`: 题库搜索权限
- `tiku.view`: 题目查看权限
- `tiku.answer`: 答题提交权限
- `*`: 全部权限

## API请求记录

### 记录说明

为了提供更好的服务监控和数据分析，系统会自动记录所有API请求的详细信息：

#### 记录内容
- **请求信息**: API密钥、请求方法、URL、端点、请求头、查询参数、请求体
- **响应信息**: 状态码、响应头、响应体、响应时间
- **客户端信息**: IP地址、用户代理
- **时间信息**: 请求时间、处理时间

#### 记录方式
- **统一记录**: 所有请求在系统中统一记录为POST方法，便于数据分析
- **原始保留**: 实际的HTTP方法（GET、POST、PUT、DELETE等）保存在请求头中
- **完整追踪**: 包括成功请求、失败请求、错误请求的完整记录

#### 数据用途
- **性能监控**: 分析API响应时间和性能指标
- **使用统计**: 统计API调用频率和使用模式
- **错误追踪**: 快速定位和解决API调用问题
- **安全审计**: 监控异常访问和安全威胁
- **业务分析**: 基于API使用数据进行业务决策

#### 隐私保护
- **敏感信息过滤**: 自动过滤Authorization、X-API-Key等敏感请求头
- **数据安全**: 所有记录数据都经过安全处理和存储
- **访问控制**: 只有授权管理员可以查看请求记录

## 接口列表

### 1. 搜索题库

**接口地址**: `POST /api/tiku/search`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| keyword | string | 是 | 搜索关键词，1-100字符 | "机动车" |
| limit | integer | 否 | 返回数量，1-50，默认5 | 10 |
| subject | integer | 否 | 科目筛选：1=学法减分，2=模拟科目 | 1 |
| category | integer | 否 | 题目类型：1=单选题，2=多选题，3=判断题，4=识图题 | 1 |
| difficulty | integer | 否 | 难度等级：1=简单，2=中等，3=困难 | 1 |

**请求示例**:
```json
POST /api/tiku/search
Content-Type: application/json
X-API-Key: tk_your_api_key_here

{
    "keyword": "机动车",
    "limit": 5,
    "subject": 1,
    "category": 3
}
```

**返回示例**:
```json
{
    "code": 200,
    "message": "搜索成功",
    "data": {
        "keyword": "机动车",
        "total": 2,
        "items": [
            {
                "id": 1,
                "subject": 1,
                "subject_name": "学法减分",
                "category": 3,
                "category_name": "判断题",
                "title": "机动车在道路上发生故障，需要停车排除故障时，驾驶人应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放。",
                "image_url": null,
                "options": {
                    "A": "正确",
                    "B": "错误",
                    "C": null,
                    "D": null,
                    "E": null,
                    "F": null
                },
                "answer": "A",
                "explanation": "根据《道路交通安全法》规定，机动车在道路上发生故障时，应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放。",
                "difficulty": 1,
                "difficulty_name": "简单",
                "view_count": 15,
                "accuracy_rate": 85.5,
                "score": 2.5,
                "created_at": "2024-01-01T00:00:00.000000Z"
            }
        ]
    }
}
```

### 2. 获取题目详情

**接口地址**: `POST /api/tiku/{id}`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 题目ID |

**请求示例**:
```json
POST /api/tiku/1
Content-Type: application/json
X-API-Key: tk_your_api_key_here

{}
```

**返回示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "id": 1,
        "subject": 1,
        "subject_name": "学法减分",
        "category": 3,
        "category_name": "判断题",
        "title": "机动车在道路上发生故障，需要停车排除故障时，驾驶人应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放。",
        "image_url": null,
        "options": {
            "A": "正确",
            "B": "错误",
            "C": null,
            "D": null,
            "E": null,
            "F": null
        },
        "answer": "A",
        "explanation": "根据《道路交通安全法》规定，机动车在道路上发生故障时，应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放。",
        "difficulty": 1,
        "difficulty_name": "简单",
        "view_count": 16,
        "accuracy_rate": 85.5,
        "created_at": "2024-01-01T00:00:00.000000Z"
    }
}
```

### 3. 提交答案

**接口地址**: `POST /api/tiku/{id}/answer`

**路径参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | integer | 是 | 题目ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| answer | string | 是 | 用户答案，最多10字符 | "A" 或 "A,B,C" |

**请求示例**:
```json
POST /api/tiku/1/answer
Content-Type: application/json
X-API-Key: tk_your_api_key_here

{
    "answer": "A"
}
```

**返回示例**:
```json
{
    "code": 200,
    "message": "提交成功",
    "data": {
        "is_correct": true,
        "user_answer": "A",
        "correct_answer": "A",
        "explanation": "根据《道路交通安全法》规定，机动车在道路上发生故障时，应当立即开启危险报警闪光灯，将机动车移至不妨碍交通的地方停放。",
        "accuracy_rate": 86.2
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 搜索功能说明

1. **全文搜索**: 优先使用MySQL的FULLTEXT搜索，支持自然语言模式
2. **备选搜索**: 当全文搜索无结果时，自动使用LIKE模糊搜索
3. **搜索范围**: 题目标题、选项A-F、答案解析
4. **结果排序**: 按相关性评分降序排列
5. **状态过滤**: 只返回启用状态的题目

## 使用示例

### cURL示例

```bash
# 搜索题库
curl -X POST "http://tiku.uzdns.com/ api/tiku/search" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: tk_your_api_key_here" \
     -d '{"keyword":"机动车","limit":5}'

# 获取题目详情
curl -X POST "http://tiku.uzdns.com/ api/tiku/1" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: tk_your_api_key_here" \
     -d '{}'

# 提交答案
curl -X POST "http://tiku.uzdns.com/ api/tiku/1/answer" \
     -H "Content-Type: application/json" \
     -H "X-API-Key: tk_your_api_key_here" \
     -d '{"answer":"A"}'
```

### JavaScript示例

```javascript
// 搜索题库
fetch('/api/tiku/search', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'tk_your_api_key_here'
    },
    body: JSON.stringify({
        keyword: '机动车',
        limit: 5
    })
})
.then(response => response.json())
.then(data => console.log(data));

// 获取题目详情
fetch('/api/tiku/1', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'tk_your_api_key_here'
    },
    body: JSON.stringify({})
})
.then(response => response.json())
.then(data => console.log(data));

// 提交答案
fetch('/api/tiku/1/answer', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'tk_your_api_key_here'
    },
    body: JSON.stringify({answer: 'A'})
})
.then(response => response.json())
.then(data => console.log(data));
```

## 更新说明

### v2.0 - POST方法统一和Header密钥验证

**重要变更**:
1. **仅支持POST方法**: 移除了GET请求的支持，所有API接口只接受POST请求
2. **Header密钥验证**: 移除了查询参数方式的API密钥传递，只支持Header方式
3. **统一请求记录**: 所有API请求在系统中统一记录为POST方法，便于数据分析

**迁移指南**:
- 将所有GET请求改为POST请求
- 将查询参数移到请求体中作为JSON数据
- 使用`X-API-Key`或`Authorization: Bearer`头部传递API密钥
- 确保设置`Content-Type: application/json`请求头

**兼容性**:
- 不再向后兼容GET请求和查询参数密钥
- 使用旧方式的请求将返回405或401错误

<?php

namespace app\store\controller;

use app\common\service\JiebaTokenizer;
use app\common\service\TNTSearchService;
use app\store\model\camera\Cloud;
use app\store\model\camera\Exam;
use app\store\model\camera\Question;
use app\store\model\camera\Subject;
use app\store\model\Store as StoreModel;
use think\Config;
use think\Db;

/**
 * 后台首页
 * Class Index
 * @package app\store\controller
 */
class Index extends Controller
{
    /**
     * 后台首页
     * @return mixed
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function index()
    {
      
        return $this->fetch('index');
    }
    
    public function upload()
    {
        
          $file_name=$_FILES['file']['name'];
        $file_type=$_FILES['file']['type'];
        $file_size=$_FILES['file']['size'];
        $file_tmp_name=$_FILES['file']['tmp_name'];
        $time=time();


        if ($file_type=="image/png"||$file_type=="image/jpeg")
        {
            $logo=$time.$file_name;
        
            move_uploaded_file($file_tmp_name,"../web/".$logo);

            $result=[
                'msg'=>'ok',
                'code'=>1,
                'src'=> "http://".$_SERVER['HTTP_HOST']."/".$logo,
                'size'=>$file_size
            ];

            return  json_encode($result);

        }else{
            $result=[
                'msg'=>'file type err',
                'code'=>0
            ];
            return json_encode($result);
        }
    }
    

    public function delete_tiku($id)
    {
        $res=Db::execute("delete from yoshop_camera_question where question_id='$id'");
          if ($res>0) {
            return $this->renderSuccess('删除成功');
        }
        return $this->renderError($model->getError() ?: '删除失败');
        
        
    }
   public function tiku()
    {
      
        
             

        $Res= Db::query("select * from yoshop_camera_question order by question_id desc ");
        //=渲染列表================================================================================================================
        $len=7;   //每页长度
        $num=count($Res);//总数
        $page=ceil($num/$len);

        if (isset($_GET['now_page'])) {$now_page=$_GET['now_page'];}else{$now_page=1;} //设定当前页
        $last_page=$now_page-1; if ($last_page<1){$last_page=1;}//上一页
        $next_page=$now_page+1;if ($next_page>$page){$next_page=$page;}//下一页
        $limit=($now_page-1)*$len;

        $res1= Db::query("select * from yoshop_camera_question order by question_id desc limit $limit,$len");
      

//        dump($qiuzu_res);
//        dump($user_res);
       $this->assign("now_page",$now_page);
         $this->assign("last_page",$last_page);
         $this->assign("next_page",$next_page);
      
         $this->assign("num",$num);
        $this->assign("page",$page);
       $this->assign("len",$len);
        //分页查询============================================================================================================

       
       $this->assign("res",$res1);
        return $this->fetch('tiku');
    }
  public function add_tiku()
    {
        
        
     
        return $this->fetch('add_tiku');
    }
      public function add_tiku_do()
    {
       $index=$_POST['data'];
       
        $timu=$index['timu'];
          $imgUrl=$index['imgUrl'];
            $a=$index['a'];
              $b=$index['b'];
                $c=$index['c'];
                  $d=$index['d'];
                    $e=$index['e'];
                      $f=$index['f'];
                      $daan=$index['daan'];
                      $jiexi=$index['jiexi'];
                      $kemu=$index['kemu'];
                      
                    //  $data['options']='A:'.$a.",B:".$b.",C:".$c.",D:".$d.",E:".$e.",F:".$f;
                    //  $data['question']=$timu;
                      $data['solve']=$jiexi;//解析
                      $data['imgUrl']=$imgUrl;
                   
                      $data['type']=1;//选择题
                      $data['answer']=0;
                      $answerarr=array('A'=>1,'B'=>2,'C'=>3,'D'=>4,'E'=>5,'F'=>6);
                      $daanarr=explode(',',$daan);
                      $newarr=array();
                      $answerstr='';
                     
                              
                          foreach($daanarr  as $dak=>$dav){
                             if(count($daanarr)==1){
                                $answerstr=$answerarr[$dav];
                             }else{
                               $newarr[]= $answerarr[$dav];
                               $answerstr=implode(',',$newarr);
                             }
                              
                          }
                          $data['answer']=$answerstr;
                      /*var_dump($answerstr);
                      //exit();
                      if($daan=='A')
                      {
                          $data['answer']=1;
                      }
                      if($daan=='B')
                      {
                          $data['answer']=2;
                      }
                          if($daan=='C')
                      {
                          $data['answer']=3;
                      }
                          if($daan=='D')
                      {
                          $data['answer']=4;
                      }
                          if($daan=='E')
                      {
                          $data['answer']=5;
                      }
                          if($daan=='F')
                      {
                          $data['answer']=6;
                      }*/
                      $data['subject']=$timu;
                    //  $data['model']='car';
                    //  $data['subject_type']=$kemu;
                      $data['optionA']=$a;
                      $data['optionB']=$b;
                      $data['optionC']=$c;
                      $data['optionD']=$d;
                      $data['optionE']=$e;
                      $data['optionF']=$f;
                      $data['create_time']=time();
                      $data['update_time']=time();
                     $res= Db::name("camera_question")->insert($data);
                     if($res>0)
                     {
                         $result=[
                             'msg'=>'ok',
                             'code'=>1
                             ];
                             echo json_encode($result);
                     }else{
                         
                                $result=[
                             'msg'=>'err',
                             'code'=>0
                             ];
                             echo json_encode($result);
                     }
                      
                      
                      
        
    
    }
    
    public function search()
    {
        
          $res=Db::query("select * from yoshop_search_jilu order by id desc ");
        
        
        
        $this->assign("list",$res);
          $this->assign("num",count($res));
        return $this->fetch('search');
    }
public function del()
{
    $id=$_POST['id'];
    $res=Db::execute("delete from yoshop_search_jilu where id='$id'");
    if($res>0)
    {
        $result=[
            'msg'=>'ok',
             'code'=>1
            ];
        
        echo json_encode($result);
    }else {
        $result=[
            'msg'=>'err',
             'code'=>0
            ];
        
        echo json_encode($result);
    }
    
    
    
}
    public function NoRand($begin = 0, $end = 20, $limit = 5)
    {
        $rand_array = range($begin, $end);
        shuffle($rand_array);//调用现成的数组随机排列函数
        return array_slice($rand_array, 0, $limit);//截取前$limit个
    }

    public function exam()
    {
//        $db = Config::get('00913');
//        $res = \db("camera_subject_img")->insertAll($db);
//        var_dump($res);die;
//        $model = new Question();
//        echo "<pre>";
//        for ($i=0;$i<10;$i++){
//            $orderSn = 'YC' .substr(time(), -3) . substr(microtime(), 2, 5) . sprintf('%02d', rand(0, 99));
//            /**飞鸟慕鱼博客 */
//            var_dump($orderSn);
//        }
//        $number = $this->NoRand();
//        $result = $model->allowField(true)->saveAll($db);
//        var_dump($result);
//        die;
//        $db = Config::get('cloud_db_4_7');
//        $cloud = new Cloud();
//        $result = $cloud->allowField(true)->saveAll($db);
//        echo "<pre>";
//        var_dump($result);die;
    }

}

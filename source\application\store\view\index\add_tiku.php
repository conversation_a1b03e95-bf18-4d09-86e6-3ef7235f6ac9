 <!-- 内容区域 start -->
   <head>


    <link rel="stylesheet" href="//unpkg.com/layui@2.6.8/dist/css/layui.css">

    <!-- 引入 layui.js -->
    <script src="//unpkg.com/layui@2.6.8/dist/layui.js"></script>

    <!-- 引入 layui.js -->
    <!--  <script src="//unpkg.com/layui@2.6.8/dist/layui.js"></script>-->

</head>
   
   
    <div class="tpl-content-wrapper " style="margin-left: -10px">
        <div class="row-content am-cf">
    <div class="row">
         <div class="widget-head am-cf">
                    <div class="widget-title am-cf">添加题库</div>
                </div>
        <div class="am-u-sm-12 am-u-md-12 am-u-lg-12">
            <div class="widget am-cf">
            <form class="layui-form" action="">
            <div class="layui-form-item">
                <label class="layui-form-label">题目</label>
                <div class="layui-input-block">
                    <input type="text" name="timu"   required  lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">

                </div>
            </div>
            <div class="layui-col-md4">
                        <label class="layui-form-label">科目<span style="color:red"></span></label>
                        <div class="layui-input-block">
                            <select lay-verify="" name="kemu" id="select_base_cityname" lay-filter="select_base_cityname" xm-select="select_base_cityname" xm-select-type="1">
                                <option value=""></option>
                                <option value="1">科目一</option>
                                <option value="2">科目四</option>
                             
                            </select>
                        </div>
          <div class="layui-form-item">
                <label class="layui-form-label">图片</label>
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" id="test2">图片上传</button>
                    <blockquote class="layui-elem-quote layui-quote-nm" style="margin-top: 10px;">
                        预览图：
                        <div class="layui-upload-list" id="demo2"></div>
                    </blockquote>
                    <input type="hidden" id="logo" name="imgUrl" >
                </div>
            </div>
     
        
            <div class="layui-form-item">
                <label class="layui-form-label">A</label>
                <div class="layui-input-block">
                    <input type="text" name="a"
                           required  lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">

                </div>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label">B</label>
                <div class="layui-input-block">
                    <input type="text" name="b" required  lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
          
                <div class="layui-form-item">
                <label class="layui-form-label">C</label>
                <div class="layui-input-block">
                    <input type="text" name="c" required  lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
                <div class="layui-form-item">
                <label class="layui-form-label">D</label>
                <div class="layui-input-block">
                    <input type="text" name="d" required  lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
                 <div class="layui-form-item">
                <label class="layui-form-label">E</label>
                <div class="layui-input-block">
                    <input type="text" name="e" required  lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
                 <div class="layui-form-item">
                <label class="layui-form-label">F</label>
                <div class="layui-input-block">
                    <input type="text" name="f" required  lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">
                </div>
            </div>
            
                <div class="layui-form-item">
                <label class="layui-form-label">答案</label>
                <div class="layui-input-block">
                    <input type="text" name="daan"
                           required  lay-verify="required" placeholder="请输入A或者B格式" autocomplete="off" class="layui-input">
                           
                           

                </div>
            </div>
                    <div class="layui-form-item">
                <label class="layui-form-label">解析</label>
                <div class="layui-input-block">
                    <input type="text" name="jiexi"
                           required  lay-verify="" placeholder="请输入" autocomplete="off" class="layui-input">

                </div>
            </div>
          
       


            <!--      <div class="layui-form-item">-->
            <!--        <label class="layui-form-label">实用对象</label>-->
            <!--        <div class="layui-input-block">-->
            <!--          <input type="text" name="title" required  lay-verify="required" placeholder="请输入" autocomplete="off" class="layui-input">-->

            <!--        </div>-->
            <!--      </div>-->



            <div class="layui-form-item">
                <div class="layui-input-block">
                    <button type="button" class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>
                    <button type="reset" class="layui-btn layui-btn-primary">重置</button>
                </div>
            </div>
        </form>
            </div>
        </div>
    </div>
</div>
    </div>
    <!-- 内容区域 end -->

    
    <script>

    var file_list=[]
    //上传图片
    layui.use('upload', function(){
        var upload = layui.upload;
        
        
           //执行实例
    var uploadInst = upload.render({
      elem: '#test2' //绑定元素
      ,url: '/index.php?s=/store/index/upload' //上传接口
      ,done: function(res){
          if(res.code==0)
          {
              layer.alert("上传失败",{icon:0})
              return false
          }
        console.log(res)
        var src=res.src
        var logo=document.getElementById("logo")
        
         $('#demo2').append('<img src="'+ src +'"  class="layui-upload-img" style="width: 150px;height: 150px">')
         
        logo.value= src
        //上传完毕回调
      }
      ,error: function(){
        //请求异常回调
      }
    });
    
/*
        //执行实例
        //多图片上传
       upload.render({
            elem: '#test2'
            ,url: '/index.php?s=/store/index/upload' //此处配置你自己的上传接口即可
            ,multiple: true
            ,before: function(obj){
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#demo2').append('<img src="'+ result +'" alt="'+ file.name +'" class="layui-upload-img">')
                });
            }
            ,done: function(res){
                 console.log(res)
                var url=res['value']
                console.log(url)
                file_list.push(url)
                //上传完毕
            }
        });
        */
    });
    //监听提交
    layui.use('form', function(){
        var form = layui.form;

        //监听提交
        form.on('submit(formDemo)', function(data){
            console.log(data)
         

      
            $.ajax({
                url:"/index.php?s=/store/index/add_tiku_do",
                type:"post",
                data:{
                  data:data.field
                },
                success(res)
                {
                    console.log(res)
                   // return false
                    var data=JSON.parse(res)
                    
                    if(data.code==1)
                    {
                        layer.alert('添加成功',{icon:6})
                    }else{
                        
                        layer.alert('网络发生错误',{icon:5})
                    }
                 
                 setTimeout(function(){
                     window.location.href=''
                     
                 },2000)


                }
            })
         
        });
    });
</script>


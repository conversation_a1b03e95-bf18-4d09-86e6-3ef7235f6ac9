<?php return array (
  0 => 
  array (
    'id' => 3493,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员从业资格包括巡游出租汽车驾驶员从业资格和网络预约出租汽车驾驶员从业资格等。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  1 => 
  array (
    'id' => 3494,
    'typeId' => '16',
    'type' => 0,
    'subject' => '申请人若首次参加出租汽车驾驶员从业资格考试，其全国公共科目和区域科目考试可在不同的区域完成。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  2 => 
  array (
    'id' => 3495,
    'typeId' => '16',
    'type' => 0,
    'subject' => '投入运营的出租汽车车辆应当安装具有行驶记录功能的车辆卫星定位装置、应急报警装置。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  3 => 
  array (
    'id' => 3496,
    'typeId' => '16',
    'type' => 0,
    'subject' => '根据国家关于深化出租汽车改革的要求，严禁出租汽车企业向驾驶员收取高额抵押金，现有抵押金过高的要降低。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  4 => 
  array (
    'id' => 3497,
    'typeId' => '16',
    'type' => 0,
    'subject' => '新增巡游出租汽车经营权全部实行无偿使用，但可以变更经营主体。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  5 => 
  array (
    'id' => 3498,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车综合服务区应当为进入服务区的出租汽车驾驶员免费提供餐饮、修理等服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  6 => 
  array (
    'id' => 3499,
    'typeId' => '16',
    'type' => 0,
    'subject' => '城市人民政府要优先发展公共交通，适度发展出租汽车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  7 => 
  array (
    'id' => 3500,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车和网络预约出租汽车应实行错位发展和差异化经营，为社会公众提供品质化、多样化的运输服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  8 => 
  array (
    'id' => 3501,
    'typeId' => '16',
    'type' => 0,
    'subject' => '取得从业资格证的出租汽车驾驶员，应当经出租汽车行政主管部门从业资格注册后，方可从事出租汽车客运服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  9 => 
  array (
    'id' => 3502,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网络预约出租汽车驾驶员的注册，通过出租汽车经营者向发证机关所在地出租汽车行政主管部门报备完成。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  10 => 
  array (
    'id' => 3503,
    'typeId' => '16',
    'type' => 0,
    'subject' => '个体出租汽车经营者自己驾驶出租汽车从事经营活动的，不需要申请办理从业资格注册手续。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  11 => 
  array (
    'id' => 3504,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应到其所属的出租汽车企业完成注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  12 => 
  array (
    'id' => 3505,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在从业资格注册有效期内，与出租汽车经营者解除劳动合同的，应当申请注销从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  13 => 
  array (
    'id' => 3506,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员取得从业资格证后超过1年未申请注册的，注册后上岗前应当完成不少于27学时的继续教育。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  14 => 
  array (
    'id' => 3507,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员继续教育由出租汽车经营者组织实施。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  15 => 
  array (
    'id' => 3508,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员办理注册后应将从业资格证交由出租汽车经营者保管。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  16 => 
  array (
    'id' => 3509,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车按照高品质服务、差异化经营的原则，有序发展。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  17 => 
  array (
    'id' => 3510,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司承担承运人责任，应当保证运营安全，保障乘客合法权益。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  18 => 
  array (
    'id' => 3511,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司暂停或者终止运营的，应当通告提供服务的车辆所有人和驾驶员。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  19 => 
  array (
    'id' => 3512,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司应与驾驶员签订劳动合同或者协议，明确双方的权利和义务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  20 => 
  array (
    'id' => 3513,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司在提供网约车服务时，应当提供驾驶员姓名、照片、手机号码和服务评价结果，以及车辆牌照等信息。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  21 => 
  array (
    'id' => 3514,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司不得妨碍市场公平竞争，不得为排挤竞争对手而以低于成本的价格运营扰乱正常市场秩序。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  22 => 
  array (
    'id' => 3515,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车驾驶员应当为乘客购买承运人责任险。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  23 => 
  array (
    'id' => 3516,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车平台公司和驾驶员可以向第三方有偿提供驾驶员、约车人和乘客的相关个人信息。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  24 => 
  array (
    'id' => 3517,
    'typeId' => '16',
    'type' => 0,
    'subject' => '任何企业和个人不得向未取得合法资质的车辆、驾驶员提供信息对接开展网约车经营服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  25 => 
  array (
    'id' => 3518,
    'typeId' => '16',
    'type' => 0,
    'subject' => '任何企业和个人不得以私人小客车合乘名义提供网约车经营服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  26 => 
  array (
    'id' => 3519,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车车辆和驾驶员不得通过未取得经营许可的网络服务平台提供运营服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  27 => 
  array (
    'id' => 3520,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网约车只要行驶里程未达到60万千米，就可以允许经营。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  28 => 
  array (
    'id' => 3521,
    'typeId' => '16',
    'type' => 0,
    'subject' => '使用年限达到8年但行驶里程未达到60万千米的网约车，应当退出经营。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  29 => 
  array (
    'id' => 3522,
    'typeId' => '16',
    'type' => 0,
    'subject' => '小、微型非营运载客汽车登记为预约出租客运的，行驶里程达到60万千米时强制报废。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  30 => 
  array (
    'id' => 3523,
    'typeId' => '16',
    'type' => 0,
    'subject' => '除小、微型非营运载客汽车外，其他小、微型营运载客汽车登记为预约出租客运的，按照该类型营运载客汽车报废标准和网约车报废标准中先行达到的标准报废。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  31 => 
  array (
    'id' => 3524,
    'typeId' => '16',
    'type' => 0,
    'subject' => '国家鼓励巡游出租汽车实行规模化、集约化、个体化经营。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  32 => 
  array (
    'id' => 3525,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应当做好运营前例行检查，保持车辆设施、设备完好，车容整洁。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  33 => 
  array (
    'id' => 3526,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应当衣着整洁，语言文明，主动问候，提醒乘客根据意愿选择是否系安全带。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  34 => 
  array (
    'id' => 3527,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应当根据乘客意愿升降车窗玻璃及使用空调、音响、视频等服务设备。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  35 => 
  array (
    'id' => 3528,
    'typeId' => '16',
    'type' => 0,
    'subject' => '为提高车辆使用效率，出租汽车驾驶员在客流高峰时段，可以不经乘客同意搭载其他乘客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  36 => 
  array (
    'id' => 3529,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应当遵守道路交通安全法规，文明礼让行车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  37 => 
  array (
    'id' => 3530,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员发现乘客遗失财物，设法及时归还失主.无法找到失主的，可由自己保存。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  38 => 
  array (
    'id' => 3531,
    'typeId' => '16',
    'type' => 0,
    'subject' => '乘客要求去偏远、冷僻地区或者夜间要求驶出城区的，出租汽车驾驶员有权直接拒绝提供服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  39 => 
  array (
    'id' => 3532,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员发现乘客遗留可疑危险物品的，应当立即报警。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  40 => 
  array (
    'id' => 3533,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，应当按照约定时间到达约定地点.乘客未按约定候车时，驾驶员可直接自行撤单。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  41 => 
  array (
    'id' => 3534,
    'typeId' => '16',
    'type' => 0,
    'subject' => '驾驶员有私自转包经营等违法行为的，巡游出租汽车经营者应当予以纠正；情节严重的，可按照约定解除合同。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  42 => 
  array (
    'id' => 3535,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车经营者应当通过建立替班驾驶员队伍、减免驾驶员休息日经营承包费用等方式保障出租汽车驾驶员休息权。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  43 => 
  array (
    'id' => 3536,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车经营者应当合理确定承包、管理费用，可以适当向驾驶员转嫁投资和经营风险。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  44 => 
  array (
    'id' => 3537,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车经营者应当根据经营成本、运价变化等因素及时调整承包费标准或者定额任务等。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  45 => 
  array (
    'id' => 3538,
    'typeId' => '16',
    'type' => 0,
    'subject' => '国家鼓励、支持和引导出租汽车企业、行业协会与出租汽车驾驶员、工会组织平等协商，合理确定并动态调整出租汽车承包费标准或定额任务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  46 => 
  array (
    'id' => 3539,
    'typeId' => '16',
    'type' => 0,
    'subject' => '有交通肇事犯罪记录的出租汽车驾驶员，将由发证机关撤销其从业资格证，并公告作废。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  47 => 
  array (
    'id' => 3540,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员不再具备从业条件的，出租汽车行政主管部门将依据相关法律法规撤销或者吊销其从业资格证件。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  48 => 
  array (
    'id' => 3541,
    'typeId' => '16',
    'type' => 0,
    'subject' => '转借、出租、涂改从业资格证的，由县级以上出租汽车行政主管部门责令改正，并处1万元以上3万元以下的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  49 => 
  array (
    'id' => 3542,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车是城市公共交通的组成部分。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  50 => 
  array (
    'id' => 3543,
    'typeId' => '16',
    'type' => 0,
    'subject' => '新增和更新出租汽车，应当使用新能源汽车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  51 => 
  array (
    'id' => 3544,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车驾驶员如无法继续从事经营的，可以将承包经营权转包给其他驾驶员。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  52 => 
  array (
    'id' => 3545,
    'typeId' => '16',
    'type' => 0,
    'subject' => '新增出租汽车经营权一律实行期限制，不得再实行无期限制。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  53 => 
  array (
    'id' => 3546,
    'typeId' => '16',
    'type' => 0,
    'subject' => '取得从业资格证件但在考核周期内未注册在岗的，仍需参加出租汽车驾驶员服务质量信誉考核。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  54 => 
  array (
    'id' => 3547,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员服务质量信誉考核工作每年进行一次。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  55 => 
  array (
    'id' => 3548,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员一个考核周期届满，经签注服务质量信誉考核等级后，该考核周期内的扣分与加分予以清除，不转入下一个考核周期。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  56 => 
  array (
    'id' => 3549,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员违反服务质量信誉考核指标的，一次扣分分值分别为1分、3分、5分、10分、20分共5种。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  57 => 
  array (
    'id' => 3550,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员从业资格全国公共科目考试成绩在（）内有效。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '市域范国',
    'optionB' => '县市域范围',
    'optionC' => '省、直辖市域范围',
    'optionD' => '全国范围',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  58 => 
  array (
    'id' => 3551,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员从业资格全国公共科目考试实行（）的考试大纲。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '地市统一',
    'optionB' => '全国统一',
    'optionC' => '县内统一',
    'optionD' => '全省统一',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  59 => 
  array (
    'id' => 3552,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员取得从业资格后，并进行（），方可从事出租汽车客运服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '实习',
    'optionB' => '继续教育',
    'optionC' => '培训',
    'optionD' => '注册',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  60 => 
  array (
    'id' => 3553,
    'typeId' => '16',
    'type' => 1,
    'subject' => '拟从事出租汽车客运服务的驾驶员，应当向（）申请参加出租汽车驾驶员从业资格考试。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '设区的市级地方人民政府出租汽车行政主管部门',
    'optionB' => '大型出租汽车经营企业',
    'optionC' => '从业资格培训机构',
    'optionD' => '出租汽车行业协会',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  61 => 
  array (
    'id' => 3554,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员到从业资格证发证机关核定的范围外从事出租汽车客运服务的，应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '重新参加全国公共科目考试和当地的区域科目考试',
    'optionB' => '参加当地的区域科目考试',
    'optionC' => '参加全国公共科目考试',
    'optionD' => '参加当地的从业资格换证考试',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  62 => 
  array (
    'id' => 3555,
    'typeId' => '16',
    'type' => 1,
    'subject' => '受理注册申请的出租汽车行政主管部门应当在（）内办理完结注册手续。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '3日',
    'optionB' => '5日',
    'optionC' => '10日',
    'optionD' => '15日',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  63 => 
  array (
    'id' => 3556,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员注册有效期届满需继续从事出租汽车客运服务的，应当在有效期届满30日前，向所在地（）申请延续注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '出租汽车行政主管部门',
    'optionB' => '工商管理部门',
    'optionC' => '网信部门',
    'optionD' => '公安交管部门',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  64 => 
  array (
    'id' => 3557,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员从业资格注册有效期为（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '1年',
    'optionB' => '2年',
    'optionC' => '3年',
    'optionD' => '5年',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  65 => 
  array (
    'id' => 3558,
    'typeId' => '16',
    'type' => 1,
    'subject' => '申请从业资格注册或者延续注册的出租汽车驾驶员，应当到（）申请注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '发证机关所在地的省级交通运输主管部门',
    'optionB' => '发证机关所在地的市级交通运输主管部门',
    'optionC' => '发证机关所在地的县级交通运输主管部门',
    'optionD' => '发证机关所在地的出租汽车行政主管部门',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  66 => 
  array (
    'id' => 3559,
    'typeId' => '16',
    'type' => 1,
    'subject' => '个体巡游出租汽车经营者自己驾驶出租汽车从事经营活动的，持其（）申请注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '机动车驾驶证、从业资格证',
    'optionB' => '机动车驾驶证、车辆运营证',
    'optionC' => '身份证、机动车驾驶证',
    'optionD' => '从业资格证、车辆运营证',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  67 => 
  array (
    'id' => 3560,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员注册有效期届满需继续从事出租汽车客运服务的，应当在有效期届满前（）内申请延续注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '5日',
    'optionB' => '10日',
    'optionC' => '20日',
    'optionD' => '30日',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  68 => 
  array (
    'id' => 3561,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员在从业资格注册有效期内，与出租汽车经营者解除劳动合同或者经营合同的，应当在（）内向原注册机构报告，并申请注销注册。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '5日',
    'optionB' => '10日',
    'optionC' => '20日',
    'optionD' => '30日',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  69 => 
  array (
    'id' => 3562,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员取得从业资格证超过3年未申请注册的，注册后上岗前应当完成不少于（）学时的继续教育。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '18',
    'optionB' => '27',
    'optionC' => '54',
    'optionD' => '72',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  70 => 
  array (
    'id' => 3563,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员继续教育由（）组织实施。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '继续教育机构',
    'optionB' => '自学',
    'optionC' => '出租汽车经营者',
    'optionD' => '驾驶员培训学校',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  71 => 
  array (
    'id' => 3564,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员最近连续（）个记分周期内记满12分记录，由发证机关撤销其从业资格证，并公告作废。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '1',
    'optionB' => '2',
    'optionC' => '3',
    'optionD' => '4',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  72 => 
  array (
    'id' => 3565,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员从业资格证遗失、毁损的，应当到原发证机关办理（）手续。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '从业资格补考',
    'optionB' => '注销证件',
    'optionC' => '撤销证件',
    'optionD' => '证件补（换）发',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  73 => 
  array (
    'id' => 3566,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员因身体健康等其他原因不宜继续从事出租汽车客运服务的，由发证机关（）其从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '撤销',
    'optionB' => '核发',
    'optionC' => '注销',
    'optionD' => '补（换）发',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  74 => 
  array (
    'id' => 3567,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员达到法定退休年龄的，由发证机关（）其从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '审核延续',
    'optionB' => '补（换）发',
    'optionC' => '注销',
    'optionD' => '撤销',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  75 => 
  array (
    'id' => 3568,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员身体健康状况不再符合有关机动车驾驶员和相关从业要求且没有主动申请注销从业资格的，由发证机关（）其从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '强制追回',
    'optionB' => '撤销',
    'optionC' => '注销',
    'optionD' => '补（换）发',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  76 => 
  array (
    'id' => 3569,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员有交通肇事犯罪、危险驾驶犯罪记录，有吸毒记录，有饮酒后驾驶记录，有暴力犯罪记录，最近连续3个记分周期内记满12分记录的，由发证机关（）其从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '撤销',
    'optionB' => '强制追回',
    'optionC' => '注销',
    'optionD' => '补（换）发',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  77 => 
  array (
    'id' => 3570,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车行政主管部门应当将出租汽车驾驶员违法行为记录等作为（）的依据。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务质量信誉考核',
    'optionB' => '出租汽车企业经营权招投标',
    'optionC' => '出租汽车企业经营权延续经营',
    'optionD' => '出租汽车企业处罚标准',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  78 => 
  array (
    'id' => 3571,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员有拒载、议价等违法行为的，应当加强继续教育；情节严重的，出租汽车行政主管部门应当对其（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '延期注册',
    'optionB' => '注销从业资格证',
    'optionC' => '撤销从业资格证',
    'optionD' => '吊销从业资格证',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  79 => 
  array (
    'id' => 3572,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车行驶里程达到（）万千米时强制报废。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '50',
    'optionB' => '60',
    'optionC' => '70',
    'optionD' => '80',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  80 => 
  array (
    'id' => 3573,
    'typeId' => '16',
    'type' => 1,
    'subject' => '行驶里程未达到60万千米但使用年限达到（）的网约车，应当退出经营。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '5年',
    'optionB' => '6年',
    'optionC' => '7年',
    'optionD' => '8年',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  81 => 
  array (
    'id' => 3574,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车驾驶员不再具备从业条件或者有严重违法行为的，由县级以上出租汽车行政主管部门依据相关法律法规的有关规定（）从业资格证件。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '注销',
    'optionB' => '暂扣',
    'optionC' => '重新核发',
    'optionD' => '撒销或者吊销',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  82 => 
  array (
    'id' => 3575,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车驾驶员的行政处罚信息将被计入（）信用记录。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '价格管理部门',
    'optionB' => '公安机关',
    'optionC' => '工商部门',
    'optionD' => '驾驶员和网约车平台公司',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  83 => 
  array (
    'id' => 3576,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司所采集的个人信息和生成的业务数据，保存期限不少于（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '1年',
    'optionB' => '2年',
    'optionC' => '3年',
    'optionD' => '4年',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  84 => 
  array (
    'id' => 3577,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员违规收费的，由（）进行处罚。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '工商部门',
    'optionB' => '价格主管部门',
    'optionC' => '公安机关',
    'optionD' => '出租汽车行政主管部门',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  85 => 
  array (
    'id' => 3578,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司暂停或者终止运营的，应当提前（）向服务所在地出租汽车行政主管部门书面报告，说明有关情况，通告提供服务的车辆所有人和驾驶员，并向社会公告。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '10日',
    'optionB' => '20日',
    'optionC' => '30日',
    'optionD' => '60日',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  86 => 
  array (
    'id' => 3579,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司应当按照有关规定，与驾驶员签订多种形式的（），明确双方的权利和义务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '劳动合同或者协议',
    'optionB' => '劳动合同或者经营合同',
    'optionC' => '劳动合同',
    'optionD' => '经营合同',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  87 => 
  array (
    'id' => 3580,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司应当保证线上提供服务的驾驶员与线下实际提供服务的驾驶员一致，并将驾驶员相关信息向（）报备。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务所在地出租汽车行业协会',
    'optionB' => '服务所在地网信管理部门',
    'optionC' => '服务所在地出租汽车行政主管部门',
    'optionD' => '服务所在地工商行政管理部门',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  88 => 
  array (
    'id' => 3581,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司所采集的个人信息和生成的业务数据应当在（）存储和使用。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '中国境内',
    'optionB' => '中国本土',
    'optionC' => '中国内地',
    'optionD' => '中国大陆',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  89 => 
  array (
    'id' => 3582,
    'typeId' => '16',
    'type' => 1,
    'subject' => '《巡游出租汽车经营服务管理规定》是为规范出租汽车经营服务行为，保障（）的合法权益。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '乘客、驾驶员和巡游出租汽车经营者',
    'optionB' => '驾驶员、巡游出租汽车经营者和管理部门',
    'optionC' => '驾驶员、乘客和管理部门',
    'optionD' => '驾驶员、乘客、巡游出租汽车经营者和管理部门',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  90 => 
  array (
    'id' => 3583,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车经营者应当保障出租汽车驾驶员合法权益，依法与其签订（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '经营合同',
    'optionB' => '劳动合同或者聘用合同',
    'optionC' => '劳动合同或者经营合同',
    'optionD' => '劳动合同',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  91 => 
  array (
    'id' => 3584,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车经营服务是指（），喷涂、安装出租汽车标识，以（）及以下乘用车和驾驶劳务为乘客出行服务，并按照乘客意愿行驶，根据行驶里程和时间计费的经营活动。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '可在道路上巡游揽客、站点候客；七座',
    'optionB' => '通过预约方式承揽乘客；九座',
    'optionC' => '通过预约方式承揽乘客；七座',
    'optionD' => '可在道路上巡游揽客；九座',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  92 => 
  array (
    'id' => 3585,
    'typeId' => '16',
    'type' => 1,
    'subject' => '预约出租汽车经营服务是指以符合条件的（）及以下乘用车（），并按照乘客意愿行驶，根据行驶里程、时间或者约定计费的经营活动。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '七座；可在道路上巡游揽客',
    'optionB' => '九座；可在道路上巡游揽客',
    'optionC' => '九座；通过预约方式承揽乘客',
    'optionD' => '七座；通过预约方式承揽乘客',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  93 => 
  array (
    'id' => 3586,
    'typeId' => '16',
    'type' => 1,
    'subject' => '国家鼓励通过（）方式配置巡游出租汽车的车辆经营权。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务质量信誉考核',
    'optionB' => '服务质量招投标',
    'optionC' => '行政审批',
    'optionD' => '拍卖',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  94 => 
  array (
    'id' => 3587,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员应当在（），持本人的从业资格证件到当地道路运输管理机构签注服务质量信誉考核等级。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务质量信誉考核周期届满后30日内',
    'optionB' => '每年2月1日前',
    'optionC' => '服务质量信誉考核周期届满后60日内',
    'optionD' => '每年3月1日前',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  95 => 
  array (
    'id' => 3588,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车经营者应当根据（）等因素及时调整承包费标准或者定额任务等。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '当地GDP总量',
    'optionB' => '经营成本、运力规模',
    'optionC' => '经营成本、运价变化',
    'optionD' => '驾驶员从业年限',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  96 => 
  array (
    'id' => 3589,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员有私自转包经营等违法行为的，（）应当予以纠正。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '公安机关',
    'optionB' => '工商部门',
    'optionC' => '出租汽车行政主管部门',
    'optionD' => '出租汽车经营者',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  97 => 
  array (
    'id' => 3590,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客要求驶往偏远地区的，巡游车驾驶员认为必要时，可以要求乘客随同到就近的（）办理验证登记手续，乘客不予配合的，驾驶员有权拒绝提供服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '公安机关治安部门',
    'optionB' => '道路运输管理机构',
    'optionC' => '经营企业',
    'optionD' => '出租汽车行政管理部门',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  98 => 
  array (
    'id' => 3591,
    'typeId' => '16',
    'type' => 1,
    'subject' => '《出租汽车服务质量信誉考核办法》是为了规范出租汽车驾驶员的服务行为，建立完善出租汽车驾驶员（），提升出租汽车服务水平。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '准入机制',
    'optionB' => '运营体制',
    'optionC' => '职业保障',
    'optionD' => '诚信体系',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  99 => 
  array (
    'id' => 3592,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员服务质量信誉考核内容包括遵守法规、（）、经营行为、运营服务等四方面内容。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务质量',
    'optionB' => '车容车貌',
    'optionC' => '维护稳定',
    'optionD' => '安全生产',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  100 => 
  array (
    'id' => 3593,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员服务质量信誉考核实行基准分值为（）分的计分制，另外加分分值为（）分。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '10，20',
    'optionB' => '20，20',
    'optionC' => '10，10',
    'optionD' => '20，10',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  101 => 
  array (
    'id' => 3594,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员一个考核周期届满，经签注服务质量信誉考核等级后，该考核周期内的（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '加分予以清除',
    'optionB' => '扣分予以清除',
    'optionC' => '扣分与加分予以清除',
    'optionD' => '扣分与加分予以保留',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  102 => 
  array (
    'id' => 3595,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司及网约车驾驶员违法使用约车人、乘客个人信息的，由公安、网信等部门依照各自职责处以（）的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '1万元以上3万元以下',
    'optionB' => '2000元以上1万元以下',
    'optionC' => '500元以上1000元以下',
    'optionD' => '1000元以上5000元以下',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  103 => 
  array (
    'id' => 3596,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网约车平台公司及网约车驾驶员违法泄露约车人、乘客个人信息的，由公安、网信等部门依照各自职责处以（）的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '500元以上1000元以下',
    'optionB' => '1000元以上5000元以下',
    'optionC' => '2000元以上1万元以下',
    'optionD' => '1万元以上3万元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  104 => 
  array (
    'id' => 3597,
    'typeId' => '16',
    'type' => 1,
    'subject' => '使用失效、伪造、变造的从业资格证，驾驶出租汽车从事出租汽车经营活动的，由县级以上出租汽车行政主管部门责令改正，并处（）的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '1万元以上3万元以下',
    'optionB' => '500元以上1000元以下',
    'optionC' => '5000元以上1万元以下',
    'optionD' => '1000元以上5000元以下',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  105 => 
  array (
    'id' => 3598,
    'typeId' => '16',
    'type' => 1,
    'subject' => '转借、出租、涂改从业资格证的，由县级以上出租汽车行政主管部门责令改正，并处（）的罚款',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '5000元以上1万元以下',
    'optionB' => '500元以上1000元以下',
    'optionC' => '1万元以上3万元以下',
    'optionD' => '1000元以上5000元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  106 => 
  array (
    'id' => 3599,
    'typeId' => '16',
    'type' => 1,
    'subject' => '驾驶员未取得从业资格证，驾驶出租汽车从事经营活动的，由县级以上出租汽车行政主管部门责令改正，并处（）的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '5000元以上1万元以下',
    'optionB' => '1000元以上5000元以下',
    'optionC' => '1万元以上3万元以下',
    'optionD' => '500元以上1000元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  107 => 
  array (
    'id' => 3600,
    'typeId' => '16',
    'type' => 1,
    'subject' => '起讫点均不在许可的经营区域从事巡游出租汽车经营活动的，由县级以上地方人民政府出租汽车行政主管部门责令改正，并处以（）罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '10000元以上20000元以下',
    'optionB' => '50元以上200元以下',
    'optionC' => '5000元以上20000元以下',
    'optionD' => '500元以上2000元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  108 => 
  array (
    'id' => 3601,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员未办理注册手续驾驶出租汽车从事经营活动的，由县级以上出租汽车行政主管部门责令改正，并处（）的罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '50元以上200元以下',
    'optionB' => '3000元以上1万元以下',
    'optionC' => '200元以上2000元以下',
    'optionD' => '1000元以上3000元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  109 => 
  array (
    'id' => 3602,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员转让、倒卖、伪造巡游出租汽车相关票据的，由县级以上地方人民政府出租汽车行政主管部门责令改正，并处以（）罚款。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '50元以上200元以下',
    'optionB' => '5000元以上20000元以下',
    'optionC' => '500元以上2000元以下',
    'optionD' => '10000元以上20000元以下',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  110 => 
  array (
    'id' => 3603,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员可以在不增加行驶里程的情况下，另载他人，无需征得乘客同意。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  111 => 
  array (
    'id' => 3604,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员可以将对自己服务不满意的乘客转给其他出租汽车或中止服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  112 => 
  array (
    'id' => 3605,
    'typeId' => '16',
    'type' => 0,
    'subject' => '车辆起步前，出租汽车驾驶员应检查车门是否关好。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  113 => 
  array (
    'id' => 3606,
    'typeId' => '16',
    'type' => 0,
    'subject' => '乘客到达目的地后，在不影响道路交通和行人安全的情况下，应在允许停车路段按乘客的要求就近停车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  114 => 
  array (
    'id' => 3607,
    'typeId' => '16',
    'type' => 0,
    'subject' => '当乘客在禁停路段扬手招车时，为了减少不必要的投诉，驾驶员应停车载客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  115 => 
  array (
    'id' => 3608,
    'typeId' => '16',
    'type' => 0,
    'subject' => '载客过程中如因客观原因确需绕道时，出租汽车驾驶员应主动向乘客说明情况，如乘客不同意绕行要求下车时，可以拒绝乘客下车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  116 => 
  array (
    'id' => 3609,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员发现乘客遗失物应该及时归还或者上交。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  117 => 
  array (
    'id' => 3610,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员发现乘客遗留物品，若找不到失主，可自行处理。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  118 => 
  array (
    'id' => 3611,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员载客途中无正当理由中断服务的视为中途甩客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  119 => 
  array (
    'id' => 3612,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车运营过程中，乘客要求使用空调、音响等设备时，驾驶员可以婉言拒绝。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  120 => 
  array (
    'id' => 3613,
    'typeId' => '16',
    'type' => 0,
    'subject' => '为提供良好服务，出租汽车驾驶员只要看到乘客扬手招车，必须马上停车载客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  121 => 
  array (
    'id' => 3614,
    'typeId' => '16',
    'type' => 0,
    'subject' => '如果乘客要求下车的路段禁止停车，出租汽车驾驶员应婉拒并耐心向乘客解释，至可下车地点停车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  122 => 
  array (
    'id' => 3615,
    'typeId' => '16',
    'type' => 0,
    'subject' => '外地乘客乘坐出租汽车时询问驾驶员当地的风景名胜等与目的地无关的地方，驾驶员可以不予理睬。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  123 => 
  array (
    'id' => 3616,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员运营过程中不得向乘客推销购物、饮食和休闲娱乐等项目。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  124 => 
  array (
    'id' => 3617,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营过程中，征得乘客同意后可以在车内吸烟。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  125 => 
  array (
    'id' => 3618,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员小王接班后发现自己一点零钱也没有准备，但赚钱时间要紧，先开上路，让乘客支付零钱就可以了。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  126 => 
  array (
    'id' => 3619,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员进入服务站点后可以插队，以提高运营效率。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  127 => 
  array (
    'id' => 3620,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在交班途中，可以询问乘客目的地是否与自己交班同方向，以确定是否载客，不耽误交班。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  128 => 
  array (
    'id' => 3621,
    'typeId' => '16',
    'type' => 0,
    'subject' => '乘客上下出租汽车时，驾驶员应引导乘客由右侧上下车。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  129 => 
  array (
    'id' => 3622,
    'typeId' => '16',
    'type' => 0,
    'subject' => '乘客上出租汽车时，驾驶员应主动协助乘客提拿行李，乘客应主动关闭行李舱。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  130 => 
  array (
    'id' => 3623,
    'typeId' => '16',
    'type' => 0,
    'subject' => '为保证行车安全，出租汽车驾驶员应提醒并在必要时协助乘客系好安全带。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  131 => 
  array (
    'id' => 3624,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员出车前应检查车容车貌、车辆技术状况、燃油或燃气，并备好随车设施、工具。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  132 => 
  array (
    'id' => 3625,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员因特殊情况不能完成载客服务的一律视为拒载或甩客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  133 => 
  array (
    'id' => 3626,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网络预约出租汽车可以在巡游出租汽车营运站点候客。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  134 => 
  array (
    'id' => 3627,
    'typeId' => '16',
    'type' => 0,
    'subject' => '无论经营者采取派单机制还是抢单机制，网络预约出租汽车驾驶员收到订单信息后，必须接单。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  135 => 
  array (
    'id' => 3628,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，未到达约定上车地点时，不应提前确认车辆已到达。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  136 => 
  array (
    'id' => 3629,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，若去往乘客目的地的路况不好，可以要求乘客取消订单。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  137 => 
  array (
    'id' => 3630,
    'typeId' => '16',
    'type' => 0,
    'subject' => '向网络服务平台发送预约用车请求的人，必须是乘客本人。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  138 => 
  array (
    'id' => 3631,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，按约定时间到达上车地点，即可向经营者发送乘客上车确认信息。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  139 => 
  array (
    'id' => 3632,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应随身携带从业资格证。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  140 => 
  array (
    'id' => 3633,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，应等待乘客联系自己，确认上车时间及地点。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  141 => 
  array (
    'id' => 3634,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车辆标志全国统一。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  142 => 
  array (
    'id' => 3635,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员接单后，乘客上车后目的地发生变化的，费用按实际行程收取。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  143 => 
  array (
    'id' => 3636,
    'typeId' => '16',
    'type' => 0,
    'subject' => '网络预约出租汽车驾驶员待乘客上车后，可提示乘客使用客户端应用程序中的车辆位置信息实时分享功能。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  144 => 
  array (
    'id' => 3637,
    'typeId' => '16',
    'type' => 0,
    'subject' => '未经约车人或乘客同意，网络预约出租汽车驾驶员未按承诺到达约定上车地点提供服务的行为视为拒载。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  145 => 
  array (
    'id' => 3638,
    'typeId' => '16',
    'type' => 0,
    'subject' => '巡游出租汽车驾驶员收听到乘客电召服务需求信息后，必须提供相应服务。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  146 => 
  array (
    'id' => 3639,
    'typeId' => '16',
    'type' => 0,
    'subject' => '乘客下车时，出租汽车驾驶员可以向乘客索取小费。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  147 => 
  array (
    'id' => 3640,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员因修路等客观原因确需绕道的，可以不用向乘客说明以避免纠纷。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  148 => 
  array (
    'id' => 3641,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项做法符合《出租汽车运营服务规范》（GB/T 22485-2013）的要求？（）',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '使用音响和空调是驾驶员的权利',
    'optionB' => '乘客的遗留物品，驾驶员可自行处理',
    'optionC' => '需改变行驶路线时可以不征求乘客意见',
    'optionD' => '按乘客意愿选择合理路线',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  149 => 
  array (
    'id' => 3642,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员有权拒载（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '孕妇',
    'optionB' => '携带易燃、易爆、有毒有害等危险品的人员',
    'optionC' => '醉酒后丧失自控能力但有人陪同的人员',
    'optionD' => '残障人士',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  150 => 
  array (
    'id' => 3643,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪种情形视为拒载？（）',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '乘客携带易燃、易爆等危险物品时，出租汽车驾驶员不予载客的',
    'optionB' => '乘客夜间去偏远地区而不按规定办理登记或相关手续，出租汽车驾驶员不予载客的',
    'optionC' => '醉酒或者精神病患者乘车无陪同人员，出租汽车驾驶员不予载客的',
    'optionD' => '问清乘客去向后，出租汽车驾驶员以不熟悉通行路线为由不予载客的',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  151 => 
  array (
    'id' => 3644,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客乘坐出租汽车时，从车内往车窗外扔东西，驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '批评教育',
    'optionB' => '及时停车捡回',
    'optionC' => '默许',
    'optionD' => '礼貌劝阻',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  152 => 
  array (
    'id' => 3645,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客乘车时若将头、手臂等伸出车外，出租汽车驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '默许',
    'optionB' => '指责乘客',
    'optionC' => '礼貌劝阻',
    'optionD' => '批评教育',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  153 => 
  array (
    'id' => 3646,
    'typeId' => '16',
    'type' => 1,
    'subject' => '当乘客上、下车时，出租汽车驾驶员的下列行为中正确的是（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '在道路内侧强行并线靠边停车',
    'optionB' => '在道路中央即停即走',
    'optionC' => '在允许停车路段靠右临时停车',
    'optionD' => '在交叉路口即停即走',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  154 => 
  array (
    'id' => 3647,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在城市道路运营过程中，车辆突然发生故障，不能继续载客时，应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '按计程计价设备显示金额收费',
    'optionB' => '请乘客在车内等候，等待救援维修',
    'optionC' => '按起步价收取车费',
    'optionD' => '协助乘客换乘，免收或减收车费',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  155 => 
  array (
    'id' => 3648,
    'typeId' => '16',
    'type' => 1,
    'subject' => '搭载乘客在单行道行驶时，乘客要求掉头改变行车路线，出租汽车驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '让乘客下车换乘其他车辆',
    'optionB' => '向乘客解释清楚后改行合理路线',
    'optionC' => '满足乘客需求',
    'optionD' => '不予理睬乘客需求',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  156 => 
  array (
    'id' => 3649,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客到达目的地后，出租汽车驾驶员下列哪种做法是错误的？（）',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '向乘客交付出租汽车发票',
    'optionB' => '在确保安全的情况下，可在禁停路段让乘客下车',
    'optionC' => '按计程计价设备显示金额收费',
    'optionD' => '提醒乘客带好随身物品',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  157 => 
  array (
    'id' => 3650,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在载客运营过程中，应当根据（）使用空调。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '自己需求',
    'optionB' => '乘客需求',
    'optionC' => '经营者要求',
    'optionD' => '管理部门要求',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  158 => 
  array (
    'id' => 3651,
    'typeId' => '16',
    'type' => 1,
    'subject' => '驾驶巡游出租汽车行至禁停路段时应（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '在车流较少时可上、下乘客',
    'optionB' => '按规定在站点即停即走',
    'optionC' => '无人看管时可上、下乘客',
    'optionD' => '按乘客需要停车',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  159 => 
  array (
    'id' => 3652,
    'typeId' => '16',
    'type' => 1,
    'subject' => '客流高峰时期，巡游出租汽车在空车待租状态下遇有多名乘客扬手招车时，驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '选择距离本车行进方向最近的乘客',
    'optionB' => '根据小费多少选择乘客',
    'optionC' => '根据乘客目的地、道路拥堵状况选择乘客',
    'optionD' => '根据目的地远近选择乘客',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  160 => 
  array (
    'id' => 7310,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客携带行李乘坐出租汽车时，驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '额外收取行李运送费',
    'optionB' => '让站点管理人员放置行李',
    'optionC' => '让乘客自己放置行李',
    'optionD' => '帮助乘客放置行李',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  161 => 
  array (
    'id' => 7311,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在载客运营过程中，正确使用车内音响设备的做法是（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '按乘客要求使用',
    'optionB' => '按运营需求听取交通广播',
    'optionC' => '尽量不使用',
    'optionD' => '按自己兴趣收听节目',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  162 => 
  array (
    'id' => 7312,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中行驶路线和乘客发生分歧时，在不违反有关法律法规的前提下，驾驶员应该（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '按自己意见行驶',
    'optionB' => '按乘客意见行驶',
    'optionC' => '向出租汽车企业求助',
    'optionD' => '让乘客下车',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  163 => 
  array (
    'id' => 7313,
    'typeId' => '16',
    'type' => 1,
    'subject' => '运营过程中遇交通堵塞、道路临时封闭等确需改变行驶路线时，出租汽车驾驶员应当（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '按自己意见行驶',
    'optionB' => '到目的地后再解释',
    'optionC' => '直接绕行',
    'optionD' => '免收绕行距离的费用',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  164 => 
  array (
    'id' => 7314,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中遇到乘客不适，下列做法不正确的是（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '就近送达医院',
    'optionB' => '让乘客下车',
    'optionC' => '拨打求助电话',
    'optionD' => '尽量平稳驾驶',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  165 => 
  array (
    'id' => 7315,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中，如乘客改变目的地，驾驶员（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '可加收车费',
    'optionB' => '可让乘客下车',
    'optionC' => '应按新目的地重新选择合理路线',
    'optionD' => '将乘客直接送达原目的地',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  166 => 
  array (
    'id' => 7316,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员运送乘客途中，如乘客因故需要短暂离开出租汽车，要求驾驶员停车等候，驾驶员应（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '拒绝',
    'optionB' => '收取消费',
    'optionC' => '跟踪乘客',
    'optionD' => '尽量配合',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  167 => 
  array (
    'id' => 7317,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中遇醉酒乘客，无法明确目的地时应该（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '尽可能帮助查询或拨打公安部门求助电话',
    'optionB' => '强行将其拖下车',
    'optionC' => '将其送至出租汽车企业',
    'optionD' => '咨询出租汽车行政主管部门',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  168 => 
  array (
    'id' => 7318,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在乘客下车时，应向乘客（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '提出对本次服务评价为优',
    'optionB' => '索取联系方式',
    'optionC' => '提醒拿好随身物品',
    'optionD' => '索要小费',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  169 => 
  array (
    'id' => 7319,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营途中（）不能完成载客服务，视为拒载或甩客。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '出现车辆故障',
    'optionB' => '遇道路、气候特殊情况',
    'optionC' => '发生交通事故',
    'optionD' => '临时处理私事',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  170 => 
  array (
    'id' => 7320,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员在站点候客时，应（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '雇人协助揽客',
    'optionB' => '挑选到合适乘客立即出发',
    'optionC' => '视情插队及时载客',
    'optionD' => '按顺序排队',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  171 => 
  array (
    'id' => 7321,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网络预约出租汽车经营者对于服务过程中发生的安全责任事故等，应（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '承担先行赔付责任',
    'optionB' => '向乘客转移运输服务风险',
    'optionC' => '向驾驶员转移运输服务风险',
    'optionD' => '承担后行赔付责任',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  172 => 
  array (
    'id' => 7322,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员运营期间，遇乘客要求的行驶路线与地图导航软件规划的路线不一',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '尊重乘客意愿',
    'optionB' => '终止服务',
    'optionC' => '仍按地图导航软件规划路线行驶',
    'optionD' => '加收服务费',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  173 => 
  array (
    'id' => 7323,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员接单后，如遇乘客未按约定时间到达上车点、且无法取得联系的，驾驶员应（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '继续等待',
    'optionB' => '直接离开',
    'optionC' => '与经营者联系，经同意后方可离开',
    'optionD' => '给乘客留言后离开',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  174 => 
  array (
    'id' => 7324,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车驾驶员站点候客时，（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '服从站点管理按序排队、顺序发车、不挑乘客',
    'optionB' => '可以下车私自揽客',
    'optionC' => '根据自己需要挑选乘客',
    'optionD' => '根据乘客需要就近上客',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  175 => 
  array (
    'id' => 7325,
    'typeId' => '16',
    'type' => 1,
    'subject' => '网络预约出租汽车驾驶员小李成功接单后又接到家里的电话，要求回家接孩子放学，小李的正确做法是（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '按约定到达乘客上车地点继续提供服务',
    'optionB' => '延迟前往乘客约定地点',
    'optionC' => '关闭联系方式',
    'optionD' => '要求乘客取消订单',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  176 => 
  array (
    'id' => 7326,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客上车前，巡游出租汽车驾驶员（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '可以询问乘客目的地',
    'optionB' => '不得询问乘客目的地',
    'optionC' => '可以和乘客议价',
    'optionD' => '可以拒载',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  177 => 
  array (
    'id' => 7327,
    'typeId' => '16',
    'type' => 1,
    'subject' => '在（）情况下，出租汽车驾驶员可以谢绝或中断服务。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '因路途遥远，驾驶员不愿意前往的',
    'optionB' => '乘客携带易燃易爆危险品上车的',
    'optionC' => '乘客年龄较大、行动不便',
    'optionD' => '乘客不允许搭载其他乘客的',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  178 => 
  array (
    'id' => 7328,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车辆，使用达5年的强制报废。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  179 => 
  array (
    'id' => 7329,
    'typeId' => '16',
    'type' => 0,
    'subject' => '无障碍出租汽车不用设置专用标志。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  180 => 
  array (
    'id' => 7330,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车况良好，可由出租汽车企业向主管部门申请免去年检，直接领取年检合格证。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  181 => 
  array (
    'id' => 7331,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营过程中应根据乘客的要求使用空调、音响等设备，不得无故拒绝。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  182 => 
  array (
    'id' => 7332,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车办理有效的机动车号牌和行驶证后，即符合运营资质。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  183 => 
  array (
    'id' => 7333,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车应配备合格的消防器材，并按要求定期检查。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  184 => 
  array (
    'id' => 7334,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车辆内外可根据商家要求张贴商业广告。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  185 => 
  array (
    'id' => 7335,
    'typeId' => '16',
    'type' => 0,
    'subject' => '无障碍出租汽车应保证充足空间安放轮椅。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  186 => 
  array (
    'id' => 7336,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车辆号牌应当清晰，固定端正，无遮挡物、反光物。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  187 => 
  array (
    'id' => 7337,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车厢内应整洁、卫生，无杂物、异味。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  188 => 
  array (
    'id' => 7338,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车行李厢内可供乘客放置行李物品的空间不得少于行李厢的三分之一。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  189 => 
  array (
    'id' => 7339,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车应保持车门功能正常，车窗玻璃封闭良好，洁净明亮，无遮蔽物，升降自如。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  190 => 
  array (
    'id' => 7340,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车应当仪表完好、整洁，仪表台不得放置与运营无关的物品。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  191 => 
  array (
    'id' => 7341,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车遮阳板、化妆镜、顶棚应齐全完好洁净。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  192 => 
  array (
    'id' => 7342,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车安全带和锁扣应齐全、有效、无污渍。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  193 => 
  array (
    'id' => 7343,
    'typeId' => '16',
    'type' => 0,
    'subject' => '遇有流行传染病时，出租汽车要做好有针对性的消毒工作。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  194 => 
  array (
    'id' => 7344,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车车身内外应清洁完好，漆皮完整无损，可按自己的爱好做个性化装饰。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  195 => 
  array (
    'id' => 7345,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车座椅应牢固无塌陷，前排座椅可前后移动，倾度可调。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  196 => 
  array (
    'id' => 7346,
    'typeId' => '16',
    'type' => 0,
    'subject' => '小李驾驶的出租汽车雨刮器坏了，考虑到正值少雨季节，他可以不必立即送修，以免耽误运营。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  197 => 
  array (
    'id' => 7347,
    'typeId' => '16',
    'type' => 0,
    'subject' => '无障碍出租汽车是指由残疾人驾驶的出租汽车。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  198 => 
  array (
    'id' => 7348,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车服务标志不包括（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '暂停运营标志',
    'optionB' => '空车待租标志',
    'optionC' => '车身广告',
    'optionD' => '电召服务标志',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  199 => 
  array (
    'id' => 7349,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车应按规定进行定期检验属于（）。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '专用设施要求',
    'optionB' => '车辆基本要求',
    'optionC' => '车容车貌要求',
    'optionD' => '服务标志要求',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  200 => 
  array (
    'id' => 7618,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车车身内外清洁完好，漆皮完整无损，（）个性化装饰。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '按个人意愿做',
    'optionB' => '按行业管理规定做',
    'optionC' => '按企业要求做',
    'optionD' => '不做',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  201 => 
  array (
    'id' => 7619,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车行李箱内可供乘客放置行李物品的空间应不小于行李箱的（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '三分之ニ',
    'optionB' => '四分之三',
    'optionC' => '二分之一',
    'optionD' => '三分之ー',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  202 => 
  array (
    'id' => 7620,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车车身颜色的样式必须（）喷涂。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '按企业要求',
    'optionB' => '按出租汽车行政主管部门的要求',
    'optionC' => '按自己意愿',
    'optionD' => '按市民要求',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  203 => 
  array (
    'id' => 7621,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车经营者应按照（），在车内适当的位置设置服务质量监督卡、价格标准、乘客须知等信息。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '乘客要求',
    'optionB' => '所在企业要求',
    'optionC' => '自己意愿',
    'optionD' => '行业管理规定',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  204 => 
  array (
    'id' => 7622,
    'typeId' => '16',
    'type' => 1,
    'subject' => '巡游出租汽车应按（）的要求，在车顶安装出租汽车标志顶灯与空车待租标志联动，夜间应有照明。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '出租汽车行政主管部门',
    'optionB' => '所在企业',
    'optionC' => '城市居民',
    'optionD' => '维修企业',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  205 => 
  array (
    'id' => 7623,
    'typeId' => '16',
    'type' => 1,
    'subject' => '上路运营的出租汽车必须设置（）。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '车辆卫星定位装置',
    'optionB' => '广告标志',
    'optionC' => '倒车雷达',
    'optionD' => '真皮座套',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  206 => 
  array (
    'id' => 7624,
    'typeId' => '16',
    'type' => 1,
    'subject' => '整洁、卫生，无杂物、异味是对出租汽车（）的要求。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '专用设施',
    'optionB' => '车辆整洁卫生',
    'optionC' => '消防设施',
    'optionD' => '服务标志',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  207 => 
  array (
    'id' => 7625,
    'typeId' => '16',
    'type' => 1,
    'subject' => '在车厢内外适当位置明示出租汽车经营者名称或简称、价格标准、服务监督电话和乘客须知信息等是对巡游出租汽车（）的要求。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '车辆基本性能',
    'optionB' => '服务标志',
    'optionC' => '专用设施',
    'optionD' => '车容车貌',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  208 => 
  array (
    'id' => 7626,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项不属于出租汽车必须安装或配置的设施？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '计程计价设备',
    'optionB' => '车载卫星定位系统',
    'optionC' => '车身电子稳定系统',
    'optionD' => '消防器材',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  209 => 
  array (
    'id' => 7627,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项不属于对出租汽车号牌的要求？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '定期更新',
    'optionB' => '无遮挡物、反光物',
    'optionC' => '固定端正',
    'optionD' => '字迹清晰',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  210 => 
  array (
    'id' => 7628,
    'typeId' => '16',
    'type' => 1,
    'subject' => '车身内外清洁完好，不做个性化装饰，漆皮、外饰条完好无损是对出租汽车（）的要求。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '车容车貌',
    'optionB' => '服务标志',
    'optionC' => '专用设施',
    'optionD' => '消防设施',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  211 => 
  array (
    'id' => 7629,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项不属于对巡游出租汽车计程计价设备的要求？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '定期自行校准',
    'optionB' => '数字显示清晰',
    'optionC' => '安装位置符合规定',
    'optionD' => '发票打印准确、清晰',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  212 => 
  array (
    'id' => 7630,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车车容车貌既反映出租汽车服务质量，也反映城市的什么？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '包容程度',
    'optionB' => '文明程度',
    'optionC' => '文化底蕴',
    'optionD' => '发展速度',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  213 => 
  array (
    'id' => 7631,
    'typeId' => '16',
    'type' => 1,
    'subject' => '小王的出租汽车在接受检查时，被要求取下悬挂在后视镜上的一串风铃，这样做的原因是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '容易让乘客产生误解，引发投诉',
    'optionB' => '不符合当地的风俗习惯',
    'optionC' => '不得违规在车内悬挂影响行车安全的设施设备',
    'optionD' => '制作不精美，影响了车的美观程度',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  214 => 
  array (
    'id' => 7632,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营中按规定要求携带的证件不包括',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '从业资格证',
    'optionB' => '继续教育合格证',
    'optionC' => '机动车驾驶证',
    'optionD' => '道路运输证或网络预约出租汽车运输证',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  215 => 
  array (
    'id' => 7633,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项不属于在巡游出租汽车车厢内外显著位置明示的信息？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '出租汽车经营者名称或者简称',
    'optionB' => '服务监督电话',
    'optionC' => '当地特色景点',
    'optionD' => '价格标准',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  216 => 
  array (
    'id' => 7634,
    'typeId' => '16',
    'type' => 1,
    'subject' => '无障碍出租车应保证有充足的空间安放',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '车载电话',
    'optionB' => '视频播放设备',
    'optionC' => '车载冰箱',
    'optionD' => '轮椅',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  217 => 
  array (
    'id' => 7635,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项不是网络预约出租汽车车辆的必备条件？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '车内不应悬挂或放置影响行车安全的设施',
    'optionB' => '安装应急报警装置',
    'optionC' => '发动机应带有涡轮测压装置',
    'optionD' => '车辆轴距、排量、车龄符合所在地方政府管理部门的规定',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  218 => 
  array (
    'id' => 7636,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营过程中应做到端庄大方，举止文明。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  219 => 
  array (
    'id' => 7637,
    'typeId' => '16',
    'type' => 0,
    'subject' => '夏天天气炎热，出租汽车驾驶员在运营过程中可以穿背心，短裤。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  220 => 
  array (
    'id' => 7638,
    'typeId' => '16',
    'type' => 0,
    'subject' => '女性出租汽车驾驶员上岗时浓妆是塑造美好形象、尊重乘客的表现。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  221 => 
  array (
    'id' => 7639,
    'typeId' => '16',
    'type' => 0,
    'subject' => '男性出租汽车驾驶员发型要求整齐大方，不留怪异的发型。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  222 => 
  array (
    'id' => 7640,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员不得在车厢内吸烟、饮食。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  223 => 
  array (
    'id' => 7641,
    'typeId' => '16',
    'type' => 0,
    'subject' => '夏天天气炎热，多下雨，出租汽车驾驶员可以穿拖鞋运营。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  224 => 
  array (
    'id' => 7642,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员要给乘客以亲切感和信任感，则需注意自身形象，着装规范，仪容大方。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  225 => 
  array (
    'id' => 7643,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营过程中提倡使用普通话。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  226 => 
  array (
    'id' => 7644,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员驾驶车辆时不能吸烟，但是可以接打电话。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  227 => 
  array (
    'id' => 7645,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员与乘客交流要频繁不间断，内容文明健康。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  228 => 
  array (
    'id' => 7646,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应虚心接受乘客的批评意见，处理好与乘客的纠纷。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  229 => 
  array (
    'id' => 7647,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员不应有在乘客面前挖鼻、剔牙、搔头等行为。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  230 => 
  array (
    'id' => 7648,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营服务时不得有向车外抛物、吐痰等行为。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  231 => 
  array (
    'id' => 7649,
    'typeId' => '16',
    'type' => 0,
    'subject' => '张师傅不会讲普通话，所以不能参加出租汽车运营服务。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  232 => 
  array (
    'id' => 7650,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员运营期间应按规定着装。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  233 => 
  array (
    'id' => 7651,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在乘客上车时要与乘客有眼神接触。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  234 => 
  array (
    'id' => 7652,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在乘客表达不满时不应粗暴打断。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  235 => 
  array (
    'id' => 7653,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在征得乘客同意后，可以在车内吸烟。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  236 => 
  array (
    'id' => 7654,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项做法不符合出租汽车驾驶员服务仪容要求？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '运营前忌食有异味、有碍服务的食物',
    'optionB' => '手、脚保持洁净，指甲修剪得体',
    'optionC' => '在运营过程中举止文明，礼貌待客',
    'optionD' => '除工作装外，可以按照个人喜好着装上岗',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  237 => 
  array (
    'id' => 7655,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员按规定着装、佩戴胸卡或服务标志，衣着整洁、服饰大方，发式整齐、面目洁净，是驾驶员（）的要求。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '安全行车',
    'optionB' => '个性修饰',
    'optionC' => '形象文明',
    'optionD' => '言行文明',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  238 => 
  array (
    'id' => 7656,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员服务仪容要求不包括。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '身体无异味',
    'optionB' => '衣着得体',
    'optionC' => '正确佩戴服务标志',
    'optionD' => '穿着时尚、高档',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  239 => 
  array (
    'id' => 7657,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客间相互交谈过程中，驾驶员应当。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '打断乘客说话',
    'optionB' => '专心开车，不插话',
    'optionC' => '要求乘客保持安静',
    'optionD' => '随意插话',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  240 => 
  array (
    'id' => 7658,
    'typeId' => '16',
    'type' => 1,
    'subject' => '乘客招手，驾驶员准备停车的时候，有另外一辆出租汽车抢在其前面招呼乘客上车，驾驶员应该怎样做',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '要求抢客车辆的驾驶员赔偿损失',
    'optionB' => '与抢客车辆驾驶员据理力争',
    'optionC' => '尊重乘客乘车选择',
    'optionD' => '要求乘客乘自己的车',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  241 => 
  array (
    'id' => 7659,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中根据乘客需求使用普通话、地方方言、外语，属于（）要求。',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '服务标志',
    'optionB' => '服务用语和言行举止',
    'optionC' => '车容车貌',
    'optionD' => '服务仪容',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  242 => 
  array (
    'id' => 7660,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在行车中应该做到',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '无人监管时将垃圾抛出车外，保持车内整洁',
    'optionB' => '不吸烟、不接打电话',
    'optionC' => '摇下车窗玻璃后吸烟',
    'optionD' => '根据个人意愿收听广播节目',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  243 => 
  array (
    'id' => 7661,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中对乘客的提问应',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '及时回答，不必顾及内容真实性',
    'optionB' => '主动、耐心、如实回答或解释',
    'optionC' => '对自己不清楚的问题不予理会',
    'optionD' => '根据自己的心情好坏回答',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  244 => 
  array (
    'id' => 7662,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营过程中，对乘客的批评意见应',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '据理力争',
    'optionB' => '不予理睬',
    'optionC' => '强硬回绝',
    'optionD' => '虚心接受',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  245 => 
  array (
    'id' => 7663,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列不符合出租汽车驾驶员服务用语要求的是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '提倡掌握外语接待外国乘客',
    'optionB' => '无论乘客是否能听懂，使用常说的本地方言',
    'optionC' => '提倡使用普通话',
    'optionD' => '根据乘客需要使用地方方言',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  246 => 
  array (
    'id' => 7664,
    'typeId' => '16',
    'type' => 1,
    'subject' => '不属于出租汽车驾驶员服务用语规范的是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '您需要打开音响吗？',
    'optionB' => '你该下车了。',
    'optionC' => '请系好安全带。',
    'optionD' => '请带好您的随身物品。',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  247 => 
  array (
    'id' => 7665,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项是出租汽车驾驶员在乘客上车时使用的文明用语？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '请提前准备好车费',
    'optionB' => '请快一点',
    'optionC' => '动作快点',
    'optionD' => '你好，请上车',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  248 => 
  array (
    'id' => 7666,
    'typeId' => '16',
    'type' => 1,
    'subject' => '下列哪项是出租汽车驾驶员在乘客下车时使用的文明用语？',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '没有零钱了，就不找您了',
    'optionB' => '请抓紧时间下车',
    'optionC' => '欢迎再次乘坐',
    'optionD' => '请把钱付了',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  249 => 
  array (
    'id' => 7667,
    'typeId' => '16',
    'type' => 1,
    'subject' => '《出租汽车运营服务规范》（GB/T 22485-2013）要求出租汽车驾驶员服务用语应',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '规范标准、语言简练',
    'optionB' => '意思完整、有礼有节',
    'optionC' => '口齿伶俐、表情丰富',
    'optionD' => '规范准确、文明礼貌',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  250 => 
  array (
    'id' => 7668,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在运营服务时，乘客要求在禁停路段停车时劝阻的服务用语是什么',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '不能停，否则要罚款。',
    'optionB' => '这里怎么能停车啊！',
    'optionC' => '对不起，这里不允许停车。',
    'optionD' => '您没有看见禁停标志吗？',
    'answer' => '3',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  251 => 
  array (
    'id' => 7669,
    'typeId' => '16',
    'type' => 1,
    'subject' => '遇乘客迟迟不上车，驾驶员应当使用的服务用语是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '请您抓紧时间，不要耽误我运营。',
    'optionB' => '你还坐不坐车？',
    'optionC' => '再不上我就走了啊。',
    'optionD' => '您需要等候吗？',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  252 => 
  array (
    'id' => 7670,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员与乘客交谈时，不属于谈话忌讳的是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '个人隐私',
    'optionB' => '个人负面情绪',
    'optionC' => '未经核实的小道消息',
    'optionD' => '介绍城市景色',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  253 => 
  array (
    'id' => 7671,
    'typeId' => '16',
    'type' => 1,
    'subject' => '出租汽车驾驶员在与女性乘客交谈时，不适合询问的是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '乘客兴趣爱好',
    'optionB' => '乘客是否需要使用空调',
    'optionC' => '乘客是否需要听音乐',
    'optionD' => '乘客婚姻状况和年龄',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  254 => 
  array (
    'id' => 7672,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在服务老年乘客时应尽量避免使用紧急制动',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  255 => 
  array (
    'id' => 7673,
    'typeId' => '16',
    'type' => 0,
    'subject' => '12周岁以下（或1.4米以下）儿童不宜坐在副驾驶位。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  256 => 
  array (
    'id' => 7674,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员切忌模仿聋哑乘客动作，取笑嘲笑对方。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  257 => 
  array (
    'id' => 7675,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员应以"乘客至上"为宗旨，满足乘客对语言、用车便利、设施等方面的需求。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  258 => 
  array (
    'id' => 7676,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在接待老弱乘客时，要面带笑容、说话温和、使用尊称。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  259 => 
  array (
    'id' => 7677,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在接待老弱乘客时，要尽量快速行驶，弥补上下车时耽误的时间。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  260 => 
  array (
    'id' => 7678,
    'typeId' => '16',
    'type' => 0,
    'subject' => '儿童好奇心强，活泼好动，乘车时喜好将头、手伸出窗外，驾驶员遇儿童乘客应当着重提醒并在必要时给予训诫，保证乘车安全。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  261 => 
  array (
    'id' => 7679,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员不必尊重乘客的宗教信仰和风俗习惯，各取所好。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  262 => 
  array (
    'id' => 7680,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员在运营过程中不得有抢客、强行揽客、甩客、倒客，未经乘客同意强行拼客等行为。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  263 => 
  array (
    'id' => 7681,
    'typeId' => '16',
    'type' => 0,
    'subject' => '出租汽车驾驶员为保障安全可以随时翻包检查乘客携带行李物品。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  264 => 
  array (
    'id' => 7682,
    'typeId' => '16',
    'type' => 0,
    'subject' => '利用计程计价设备作弊收取车费是出租汽车服务中的违法行为。',
    'subjectSimple' => NULL,
    'imgUrl' => NULL,
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => NULL,
    'optionD' => NULL,
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => NULL,
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  265 => 
  array (
    'id' => 7683,
    'typeId' => '16',
    'type' => 1,
    'subject' => '对老年男性乘客使用的尊称不包括',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '老人家',
    'optionB' => '老先生',
    'optionC' => '老师傅',
    'optionD' => '老头',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  266 => 
  array (
    'id' => 7684,
    'typeId' => '16',
    'type' => 1,
    'subject' => '遇到儿童乘客乘车，应做好的服务不包括',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '提醒儿童车上有好吃的糖果',
    'optionB' => '提醒儿童注意安全',
    'optionC' => '提醒儿童不要将头手伸出窗外',
    'optionD' => '提醒儿童不要乱动车内设备',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  267 => 
  array (
    'id' => 7685,
    'typeId' => '16',
    'type' => 1,
    'subject' => '符合出租汽车驾驶员做好伤病乘客服务要求的行为是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '直接将患病乘客拉到有回扣的医院就医',
    'optionB' => '伤员无人陪同又确有需要时，驾驶员不宜主动协助办理救治手续',
    'optionC' => '对患病乘客播放摇滚乐以放松心情',
    'optionD' => '安排伤员躺卧在后排座位上',
    'answer' => '4',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  268 => 
  array (
    'id' => 7686,
    'typeId' => '16',
    'type' => 1,
    'subject' => '遇到盲人乘客乘车，出租汽车驾驶员应该',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '主动下车，将盲人搀扶至车内',
    'optionB' => '打开车门，耐心等候乘客上车',
    'optionC' => '打开车门锁，提醒乘客自行上车',
    'optionD' => '加收服务费',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  269 => 
  array (
    'id' => 7687,
    'typeId' => '16',
    'type' => 1,
    'subject' => '盲人乘客不能辨认计程计价设备，因此出租汽车驾驶员正确的收费做法是',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '按计程计价设备金额收取',
    'optionB' => '虚报计价设备显示金额',
    'optionC' => '提前议价',
    'optionD' => '加收服务费',
    'answer' => '1',
    'optionSimple' => NULL,
    'solve' => '',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
  270 => 
  array (
    'id' => 7979,
    'typeId' => '16',
    'type' => 0,
    'subject' => '驾驶机动车遇乘客无理取闹时,驾驶人应据理力争,无论自己对或错.',
    'subjectSimple' => NULL,
    'imgUrl' => '',
    'optionA' => '正确',
    'optionB' => '错误',
    'optionC' => '',
    'optionD' => '',
    'answer' => '2',
    'optionSimple' => NULL,
    'solve' => '当与乘客谈论话题意见不一致或发生纠纷时，驾驶员要尽量接受乘客意见，当场不要反驳，以免引起误解及不必要的投诉。',
    'createTime' => NULL,
    'updateTime' => NULL,
    'typeCN' => NULL,
    'userDoQuestion' => 
    array (
      'id' => NULL,
      'userId' => NULL,
      'questionId' => NULL,
      'isError' => NULL,
      'isCollect' => NULL,
      'answer' => NULL,
      'createTime' => NULL,
      'updateTime' => NULL,
    ),
    'questionType' => NULL,
  ),
);
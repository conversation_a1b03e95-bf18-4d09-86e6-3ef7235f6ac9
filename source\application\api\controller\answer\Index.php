<?php

namespace app\api\controller\answer;

use app\api\controller\Controller;
use app\api\model\camera\Exam;
use app\api\model\CameraExam;
use app\api\model\Setting;
use app\common\library\AipOcr;
use think\Db;
use think\Request;
use Exception;

class Index extends Controller
{

    protected $user;

    public function _initialize()
    {


        parent::_initialize(); // TODO: Change the autogenerated stub
        $this->user = $this->getUser(true);
        if ($this->user['card_type'] == 10 && $this->user["second"] == 0) {

            $this->throwError("搜题次数不足");
        } else if ($this->user['card_type'] != 10) {
            if (strtotime($this->user['end_time']) < time()) {
                ## 过期之后自动变回次数账户
                $this->user['card_type'] = 10;
                $this->user->save();

                $this->throwError("会员已过期");
            }
            if ($this->user["second"] <= 0) {
                $this->throwError("搜题次数不足");
            }
        }
    }

    public function add_search($text, $type, $status, $result = [])
    {
        $user        = json_decode($this->getUser(), true);
        $nickname    = $user['nickName'];
        $avatar      = $user['avatarUrl'];
        $uid         = $user['user_id'];
        $add_time    = date("Y-m-d h:i:s", time());
        $result_text = '' . serialize($result);
//        foreach ($result as $item){
//            $result_text .= $item
//        }

        $res = Db::execute("insert into yoshop_search_jilu (uid,nickname,avatar,text,result,type,status,add_time) values ('$uid','$nickname','$avatar','$text','$result_text','$type','$status','$add_time') ");
    }

    ## 搜题
    public function search($keys)
    {
        if (is_url($keys)) {
            return $this->imageSearch($keys);
        }

        return $this->textSearch($keys);
    }

    ## 图片搜题
    public function imageSearch($image_url)
    {
        // 优先使用新云题库3.0 API进行图片搜索
        $newApiResult = $this->newCloudApiImageSearch($image_url, '1');
        if ($newApiResult !== false) {
            // 新API搜索成功
            $this->add_search($image_url, 1, 1, $newApiResult);
            ## 扣次数
            $this->user->second = ['dec', 1];
            $this->user->save();

            log_write('新云题库3.0 API图片搜索成功: ' . json_encode($newApiResult));
            return $this->renderSuccess(['answer' => $newApiResult], "搜索成功");
        }

        // 新API失败，降级到OCR+文本搜索
        log_write('新云题库3.0 API失败，降级到OCR+文本搜索', 'image-search-fallback');

        $ocr    = Setting::getItem("ocr");
        $client = new AipOcr($ocr['app_id'], $ocr['api_key'], $ocr['secret_key']);
        if ($ocr["is_model"] == 10) {
            $result = $client->basicGeneralUrl($image_url);
        } else {
            $image  = file_get_contents($image_url);
            $result = $client->basicAccurate($image);
        }
        if (!isset($result['words_result']) || !$result['words_result']) {
            $this->add_search($image_url, 1, 0);

            return $this->renderError("未识别到任何图片内容");
        }
        if (!$result['words_result']) {
            $this->add_search($image_url, 1, 0);

            return $this->renderError("未识别到内容");
        }

        $text = "";
        foreach ($result['words_result'] as $values) {
            $text .= $values['words'];
        }

        $n = $this->textSearch($text);
        if ($n['code'] == 0) {
            $this->add_search($image_url, 1, 0);
        } else {

            $data   = $this->textSearch($text);
            $answer = $data['data']['answer'];

            if (count($answer) > 0) {

                $this->add_search($image_url, 1, 1);
            } else {

                $this->add_search($image_url, 1, 0);
            }

            return $data;
        }


        return $this->textSearch1($text);
    }

    ## 文本搜索
    public function textSearch1($text)
    {
        $answer = $this->longApiCloud($text);
        // 如果 云Api 没结果，本地搜索
        if (!$answer) {
            $ocr = Setting::getItem("ocr");
            if ($ocr['topic'] == 10) {
                $answer = $this->LocalCloud($text);
            } else {
                $answer = $this->longRangeCloud($text);
            }
        }


        if (!$answer) {

            return $this->renderError("未识别到有效答案");
        }
        ## 扣次数
        //        if ($this->user['card_type'] == 10) {
        //        }
        $this->user->second = [ 'dec', 1 ];
        $this->user->save();

        return $this->renderSuccess(compact("answer"), "搜索成功");
    }

    ## 文本搜索
    public function textSearch($text)
    {
        $answer = $this->longApiCloud($text);
        // 如果 云Api 没结果，本地搜索
        if (!$answer) {
            $ocr = Setting::getItem("ocr");
            if ($ocr['topic'] == 10) {
                $answer = $this->LocalCloud($text);
            } else {
                $answer = $this->longRangeCloud($text);
            }
        }

        if (!$answer) {
            $this->add_search($text, 0, 0);

            return $this->renderError("未识别到任何内容");
        }
        ## 扣次数
        //        if ($this->user['card_type'] == 10) {
        //        }
        $this->user->second = [ 'dec', 1 ];
        $this->user->save();
        log_write('answer' . json_encode($answer));
        $this->add_search($text, 0, 1, $answer);

        return $this->renderSuccess(compact("answer"), "搜索成功");
    }

    ## 本地识别
    public function LocalCloud($text)
    {
        $sql    = "SELECT *,MATCH (title,a,b,c,d,content) AGAINST ('" . $text . "' IN NATURAL LANGUAGE MODE) AS score
 FROM yoshop_camera_subject_img where MATCH (title,a,b,c,d,content) AGAINST ('" . $text . "' IN NATURAL LANGUAGE MODE) > 0 ORDER BY score DESC limit 5;";
        $result = Db::query($sql);
        if (!$result) {
            return false;
        }
        foreach ($result as $key => &$val) {
            $val["imgUrl"] = base_url() . "tiku_image/" . $val["imgUrl"];
            $val["id"]     = 'subject_img_' . ($val['id'] ?? '0');
        }

        return $result;
    }

    ## 云端识别
    public function longRangeCloud($text)
    {
        $sql    = "SELECT *,MATCH (optionA,optionB,optionC,optionD,subject) AGAINST ('" . $text . "' IN NATURAL LANGUAGE MODE) AS score
 FROM yoshop_camera_question where MATCH (optionA,optionB,optionC,optionD,subject) AGAINST ('" . $text . "' IN NATURAL LANGUAGE MODE) > 0 ORDER BY score DESC limit 5;";
        $result = Db::query($sql);
        if (!$result) {
            return false;
        }
        $data = [];
        foreach ($result as $key => $val) {
            $answer     = explode(",", $val["answer"]);
            $index[1]   = "A";
            $index[2]   = "B";
            $index[3]   = "C";
            $index[4]   = "D";
            $answer_txt = "";
            foreach ($answer as $k => $v) {
                $answer_txt .= $index[$v] . ",";
            }
            $answer_txt            = rtrim($answer_txt, ",");
            $data[$key]['a']       = $val["optionA"];
            $data[$key]['b']       = $val["optionB"];
            $data[$key]['c']       = $val["optionC"];
            $data[$key]['d']       = $val["optionD"];
            $data[$key]['content'] = $val["solve"];
            $data[$key]['title']   = $val["subject"];
            $data[$key]['dn']      = $answer_txt;
            $data[$key]["imgUrl"]  = $val["imgUrl"];
            $data[$key]["id"]      = 'question_' . ($val['question_id'] ?? 0);
        }

        return $data;
    }
    // 对接新的题库 @/docs/云题库3.0.md ，优先使用新的云题库进行搜索，未搜到继续

    /**
     * 通过新云题库3.0 API搜索 (基于图片识别)
     */
    public function longApiCloud($text)
    {
        // 获取云题库API配置
        $ocr = Setting::getItem("ocr");

        // 新云题库3.0配置
        $newApiKey = isset($ocr['new_cloud_api_key']) ? $ocr['new_cloud_api_key'] : 'ak_a3d830754e42dde3c7936697e23af6779fbb68ce797060ab5096d68fae5c6b60';
        $newApiUrl = isset($ocr['new_cloud_api_url']) ? $ocr['new_cloud_api_url'] : 'http://47.96.29.178:8080';

        // 旧API配置（作为降级）
        $oldApiKey = isset($ocr['cloud_api_key']) ? $ocr['cloud_api_key'] : 'tk_30jVXSiUhicSgEIGaf9peCJH0VdJqlc8';
        $oldApiUrl = isset($ocr['cloud_api_url']) ? $ocr['cloud_api_url'] : 'http://tiku.uzdns.com/api';

        // 首先尝试新的云题库3.0 API（基于文本搜索的模拟）
        $newResult = $this->tryNewCloudApi($text, $newApiUrl, $newApiKey);
        if ($newResult !== false) {
            return $newResult;
        }

        // 降级到旧API
        log_write('新云题库3.0 API失败，降级到旧API', 'cloud-api-fallback');
        return $this->tryOldCloudApi($text, $oldApiUrl, $oldApiKey);
    }

    /**
     * 尝试新云题库3.0 API
     * 注意：新API是基于图片的，这里我们先用文本搜索作为过渡方案
     */
    private function tryNewCloudApi($text, $apiUrl, $apiKey)
    {
        // 新API暂时不支持纯文本搜索，返回false让系统降级
        // TODO: 当有图片URL时，可以调用新API的 /api/solve 接口
        log_write('新云题库3.0 API暂不支持纯文本搜索，等待图片搜索功能。参数: text=' . $text . ', url=' . $apiUrl, 'cloud-api-info');
        return false;
    }

    /**
     * 通过云题库4.0 API搜索 (基于图片识别)
     * @param string $imageUrl 图片URL
     * @param string $questionType 题目类型 (1=驾考, 2=游戏答题, 3=智能答题) -- 4.0接口不需要此参数
     * @return array|false
     */
    public function newCloudApiImageSearch($imageUrl, $questionType = '1')
    {
        // 云题库4.0 API地址和密钥（硬编码）
        $apiUrl = 'http://solve.igmdns.com/api/v1/process-image';

        
        // 构建请求体
        $postData = [
            'image_url' => $imageUrl
        ];

        // 构建请求头
        $headers = [
            'Content-Type: application/json',

            'User-Agent: PHP-Client/1.0'
        ];

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => $apiUrl,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($postData),
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT        => 60,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS      => 3
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error    = curl_error($ch);
        curl_close($ch);

        if ($error) {
            log_write('云题库4.0 API cURL错误: ' . $error, 'cloud-api4-error');
            return false;
        }
        if ($httpCode !== 200) {
            log_write('云题库4.0 API HTTP错误: ' . $httpCode . ', 响应: ' . $response, 'cloud-api4-error');
            return false;
        }

        $result = json_decode($response, true);
        if (!$result || !isset($result['code']) || $result['code'] != 200 || !isset($result['data'])) {
            log_write('云题库4.0 API响应异常: ' . $response, 'cloud-api4-error');
            return false;
        }

        $data = [];
        foreach ($result['data'] as $idx => $item) {
            // 兼容原有 answer 数据结构
            $options = $item['options'] ?? [];
            $answerObj = $item['answer'] ?? [];
            // 处理答案格式（多选/单选/判断）
            $dn = '';
            if (is_array($answerObj)) {
                $dn = implode(',', array_keys($answerObj));
            }
            $data[$idx]['a'] = $options['A'] ?? ($options['Y'] ?? '');
            $data[$idx]['b'] = $options['B'] ?? ($options['N'] ?? '');
            $data[$idx]['c'] = $options['C'] ?? '';
            $data[$idx]['d'] = $options['D'] ?? '';
            $data[$idx]['content'] = $item['analysis'] ?? '';
            $data[$idx]['title'] = $item['question_text'] ?? '';
            $data[$idx]['dn'] = $dn;
            $data[$idx]['imgUrl'] = $item['user_image'] ?? $imageUrl;
            $data[$idx]['id'] = 'api4_' . ($idx + 1) . '_' . time();
        }
        log_write('云题库4.0 API搜索成功，图片: ' . $imageUrl, 'cloud-api4-success');
        return $data;
    }

    /**
     * 旧云题库API（保持兼容）
     */
    private function tryOldCloudApi($text, $apiUrl, $apiKey)
    {
        // 构建请求URL
        $url = $apiUrl . '/tiku/search4ocr';

        // 构建请求体数据
        $postData = [
            'keyword' => $text,
            'limit'   => 5
        ];

        try {
            // 发送POST请求
            $response = $this->curlPostWithHeaders($url, $postData, $apiKey);

            if (!$response) {
                log_write('旧云题库API响应为空', 'cloud-api-error');
                return false;
            }

            // 解析JSON响应
            $result = json_decode($response, true);

            if (!$result) {
                log_write('旧云题库API响应JSON解析失败: ' . $response, 'cloud-api-error');
                return false;
            }

            if ($result['code'] != 200) {
                log_write('旧云题库API返回错误: ' . json_encode($result), 'cloud-api-error');
                return false;
            }

            if (!isset($result['data']['items'])) {
                log_write('旧云题库API响应缺少items数据: ' . json_encode($result), 'cloud-api-error');
                return false;
            }

            // 转换数据格式以匹配longRangeCloud的返回格式
            $data = [];
            foreach ($result['data']['items'] as $key => $item) {
                // 处理答案格式
                $answer_txt = $item['matched_answer'];
                if($answer_txt =='Y' || $answer_txt =='X'){
                    $answer_txt = $item['matched_answer_text'];
                }

                // 构建选项
                $options               = $item['options_raw'];
                $data[$key]['a']       = $options['A'] ?? '';
                $data[$key]['b']       = $options['B'] ?? '';
                $data[$key]['c']       = $options['C'] ?? '';
                $data[$key]['d']       = $options['D'] ?? '';
                $data[$key]['content'] = $item['explanation'] ?? '';
                $data[$key]['title']   = $item['title'];
                $data[$key]['dn']      = $answer_txt;
                $data[$key]["imgUrl"]  = $item['image_url'] ?? '';
                $data[$key]["id"]      = 'old_api_' . ($item['id'] ?? 0);
            }

            // 记录成功日志
            log_write('旧云题库API搜索成功，关键词: ' . $text . '，结果数量: ' . count($data), 'cloud-api-success');

            return $data;

        } catch (Exception $e) {
            // 记录错误日志
            log_write('旧云题库API调用异常: ' . $e->getMessage(), 'cloud-api-error');
            return false;
        }
    }

    /**
     * 发送带Header的POST请求
     *
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @param string $apiKey API密钥
     * @return string|false
     */
    private function curlPostWithHeaders($url, $data, $apiKey)
    {
        $ch = curl_init();

        // 设置请求头
        $headers = [
            'Content-Type: application/json',
            'X-API-Key: ' . $apiKey,
            'User-Agent: PHP-Client/1.0'
        ];

        // 配置cURL选项
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT        => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS      => 3
        ]);

        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error    = curl_error($ch);

        curl_close($ch);

        // 检查cURL错误
        if ($error) {
            log_write('云题库API cURL错误: ' . $error, 'cloud-api-error');

            return false;
        }

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            log_write('云题库API HTTP错误: ' . $httpCode . ', 响应: ' . $response, 'cloud-api-error');

            return false;
        }

        return $response;
    }

    /**
     * 发送新云题库3.0 API的POST请求
     *
     * @param string $url 请求URL
     * @param array $data 请求数据
     * @param string $apiKey API密钥
     * @return string|false
     */
    private function curlPostWithNewApiHeaders($url, $data, $apiKey)
    {
        $ch = curl_init();

        // 设置请求头 (新API使用Bearer Token认证)
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'User-Agent: PHP-Client/1.0'
        ];

        // 配置cURL选项
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url,
            CURLOPT_POST           => true,
            CURLOPT_POSTFIELDS     => json_encode($data),
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_TIMEOUT        => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS      => 3
        ]);

        // 执行请求
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error    = curl_error($ch);

        curl_close($ch);

        // 检查cURL错误
        if ($error) {
            log_write('新云题库3.0 API cURL错误: ' . $error, 'new-cloud-api-error');
            return false;
        }

        // 检查HTTP状态码
        if ($httpCode !== 200) {
            log_write('新云题库3.0 API HTTP错误: ' . $httpCode . ', 响应: ' . $response, 'new-cloud-api-error');
            return false;
        }

        return $response;
    }

}

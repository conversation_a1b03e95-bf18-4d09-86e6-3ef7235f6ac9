# 拍照搜题API v3.0 接入指南

## 📋 概述

拍照搜题API是一个基于AI的智能题目识别和解答服务，支持多种题型的图片识别、答案分析和详细解析。

### ✨ 核心特性
- 🔍 **智能识别** - 支持选择题、填空题、判断题等多种题型
- 🚀 **高性能缓存** - Redis + MySQL混合缓存，响应速度快
- 🛡️ **安全可靠** - API Key认证，完整的安全防护
- 📊 **使用统计** - 完整的调用日志和统计分析
- 🌍 **高可用** - 多层降级保障，服务稳定可靠

### 🌐 服务地址
```
生产环境：http://47.96.29.178:8080
```


### 正式的Key
```
ak_a3d830754e42dde3c7936697e23af6779fbb68ce797060ab5096d68fae5c6b60
```

## 🚀 快速开始

### 1. 获取API密钥

联系管理员获取API密钥，或通过管理后台申请：
- **管理后台地址**: `http://your-domain.com/admin`
- **默认账号**: admin / admin

### 2. 基础信息

- **API基础URL**: `http://your-domain.com/api`
- **认证方式**: Bearer Token
- **请求格式**: JSON
- **响应格式**: JSON

## 🔐 认证方式

所有API请求都需要在请求头中包含有效的API密钥：

```http
Authorization: Bearer your_api_key_here
Content-Type: application/json
```

## 📡 API接口

### 1. 题目解析接口

#### **接口地址**
```
POST /api/solve
```

#### **请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| qu_type | string | 是 | 题目类型：1=驾考, 2=游戏答题, 3=其他答题 |
| img_url | string | 是 | 题目图片URL地址 |

#### **请求示例**

```bash
curl -X POST http://your-domain.com/api/solve \
  -H "Authorization: Bearer ak_your_api_key_here" \
  -H "Content-Type: application/json" \
  -d '{
    "qu_type": "1",
    "img_url": "https://example.com/question.jpg"
  }'
```

#### **响应格式**

**成功响应 (200)**:
```json
{
  "code": 200,
  "message": "解析成功",
  "data": {
    "type": "选择题",
    "question": "题目内容",
    "options": {
      "A": "选项A内容",
      "B": "选项B内容",
      "C": "选项C内容",
      "D": "选项D内容"
    },
    "answer": "A",
    "analysis": "详细解析内容..."
  }
}
```

**缓存命中响应**:
```json
{
  "code": 200,
  "message": "解析成功(Redis缓存命中)",
  "data": {
    // 同上
  }
}
```

**错误响应**:
```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

### 2. 健康检查接口

#### **接口地址**
```
GET /health
```

#### **响应示例**
```json
{
  "status": "ok",
  "timestamp": "2025-06-04T10:30:00Z"
}
```

## 📊 响应状态码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败，API密钥无效 |
| 403 | 权限不足，API密钥已禁用 |
| 429 | 请求频率超限 |
| 500 | 服务器内部错误 |

## 🔧 错误处理

### 常见错误类型

#### **认证错误 (401)**
```json
{
  "code": 401,
  "message": "认证失败",
  "error": "API密钥无效或已过期"
}
```

#### **参数错误 (400)**
```json
{
  "code": 400,
  "message": "参数错误",
  "error": "img_url不能为空"
}
```

#### **频率限制 (429)**
```json
{
  "code": 429,
  "message": "请求频率超限",
  "error": "每分钟最多60次请求"
}
```

## 📈 性能说明

### 响应时间

| 场景 | 平均响应时间 | 说明 |
|------|-------------|------|
| **缓存命中** | 1-3秒 | Redis/MySQL缓存命中 |
| **首次解析** | 10-20秒 | AI识别+分析处理 |

### 缓存机制

- **一级缓存**: Redis高速缓存，响应时间 ~2秒
- **二级缓存**: MySQL持久化缓存，响应时间 ~3秒
- **缓存策略**: 相同图片永久缓存，智能去重

## 🛡️ 安全说明

### API密钥管理
- 请妥善保管API密钥，避免泄露
- 建议定期更换API密钥
- 不要在客户端代码中硬编码API密钥

### 请求限制
- 默认限制：每分钟60次请求
- 超出限制将返回429状态码
- 可联系管理员调整限制

### 图片要求
- 支持格式：JPG、PNG、GIF
- 图片大小：建议不超过5MB
- 图片清晰度：建议分辨率不低于800x600

## 💡 最佳实践

### 1. 错误重试
```javascript
async function callAPI(data, retries = 3) {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch('/api/solve', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer your_api_key',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });
      
      if (response.ok) {
        return await response.json();
      }
      
      if (response.status === 429) {
        // 频率限制，等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }
      
      throw new Error(`HTTP ${response.status}`);
    } catch (error) {
      if (i === retries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}
```

### 2. 缓存利用
- 相同图片会自动缓存，无需重复请求
- 建议在客户端也实现本地缓存
- 可通过响应message判断是否命中缓存

### 3. 图片优化
- 上传前压缩图片以提高传输速度
- 确保图片清晰度足够AI识别
- 避免图片中包含无关内容

## 🔍 调试工具

### cURL测试
```bash
# 基础测试
curl -X POST http://your-domain.com/api/solve \
  -H "Authorization: Bearer your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"qu_type":"1","img_url":"https://example.com/test.jpg"}' \
  -v

# 健康检查
curl http://your-domain.com/health
```

### Postman配置
1. 设置请求方法为POST
2. URL: `http://your-domain.com/api/solve`
3. Headers添加：
   - `Authorization: Bearer your_api_key`
   - `Content-Type: application/json`
4. Body选择raw JSON格式

## 📞 技术支持

### 联系方式
- **技术支持**: 联系系统管理员
- **问题反馈**: 通过管理后台提交工单
- **API状态**: 访问 `/health` 接口检查服务状态

### 常见问题

**Q: API密钥在哪里获取？**
A: 联系管理员或通过管理后台申请。

**Q: 为什么返回401错误？**
A: 检查API密钥是否正确，是否已过期或被禁用。

**Q: 图片识别不准确怎么办？**
A: 确保图片清晰、完整，避免模糊或截断。

**Q: 响应时间较慢怎么办？**
A: 首次解析需要AI处理，后续相同图片会使用缓存。

## 💻 SDK示例代码

### JavaScript/Node.js

```javascript
class SolveAPI {
  constructor(apiKey, baseURL = 'http://your-domain.com/api') {
    this.apiKey = apiKey;
    this.baseURL = baseURL;
  }

  async solve(questionType, imageUrl) {
    const response = await fetch(`${this.baseURL}/solve`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        qu_type: questionType,
        img_url: imageUrl
      })
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return await response.json();
  }
}

// 使用示例
const api = new SolveAPI('your_api_key_here');
api.solve('1', 'https://example.com/question.jpg')
  .then(result => console.log(result))
  .catch(error => console.error(error));
```

### Python

```python
import requests
import json

class SolveAPI:
    def __init__(self, api_key, base_url='http://your-domain.com/api'):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }

    def solve(self, question_type, image_url):
        url = f'{self.base_url}/solve'
        data = {
            'qu_type': question_type,
            'img_url': image_url
        }

        response = requests.post(url, headers=self.headers, json=data)
        response.raise_for_status()
        return response.json()

# 使用示例
api = SolveAPI('your_api_key_here')
try:
    result = api.solve('1', 'https://example.com/question.jpg')
    print(json.dumps(result, indent=2, ensure_ascii=False))
except requests.exceptions.RequestException as e:
    print(f'API Error: {e}')
```

### PHP

```php
<?php
class SolveAPI {
    private $apiKey;
    private $baseURL;

    public function __construct($apiKey, $baseURL = 'http://your-domain.com/api') {
        $this->apiKey = $apiKey;
        $this->baseURL = $baseURL;
    }

    public function solve($questionType, $imageUrl) {
        $url = $this->baseURL . '/solve';
        $data = json_encode([
            'qu_type' => $questionType,
            'img_url' => $imageUrl
        ]);

        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode !== 200) {
            throw new Exception("API Error: HTTP $httpCode");
        }

        return json_decode($response, true);
    }
}

// 使用示例
$api = new SolveAPI('your_api_key_here');
try {
    $result = $api->solve('1', 'https://example.com/question.jpg');
    echo json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage();
}
?>
```

### Java

```java
import java.io.IOException;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.URI;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Map;

public class SolveAPI {
    private final String apiKey;
    private final String baseURL;
    private final HttpClient client;
    private final ObjectMapper mapper;

    public SolveAPI(String apiKey, String baseURL) {
        this.apiKey = apiKey;
        this.baseURL = baseURL != null ? baseURL : "http://your-domain.com/api";
        this.client = HttpClient.newHttpClient();
        this.mapper = new ObjectMapper();
    }

    public Map<String, Object> solve(String questionType, String imageUrl)
            throws IOException, InterruptedException {
        String url = baseURL + "/solve";

        Map<String, String> requestData = Map.of(
            "qu_type", questionType,
            "img_url", imageUrl
        );

        String jsonData = mapper.writeValueAsString(requestData);

        HttpRequest request = HttpRequest.newBuilder()
            .uri(URI.create(url))
            .header("Authorization", "Bearer " + apiKey)
            .header("Content-Type", "application/json")
            .POST(HttpRequest.BodyPublishers.ofString(jsonData))
            .build();

        HttpResponse<String> response = client.send(request,
            HttpResponse.BodyHandlers.ofString());

        if (response.statusCode() != 200) {
            throw new RuntimeException("API Error: HTTP " + response.statusCode());
        }

        return mapper.readValue(response.body(), Map.class);
    }
}

// 使用示例
public class Main {
    public static void main(String[] args) {
        SolveAPI api = new SolveAPI("your_api_key_here", null);
        try {
            Map<String, Object> result = api.solve("1",
                "https://example.com/question.jpg");
            System.out.println(result);
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
        }
    }
}
```

## 🔄 Webhook回调 (可选)

如果需要异步处理，可以配置Webhook回调：

### 配置方式
联系管理员配置Webhook URL，系统将在处理完成后回调。

### 回调格式
```json
{
  "request_id": "unique_request_id",
  "status": "success",
  "data": {
    "type": "选择题",
    "question": "题目内容",
    "options": {...},
    "answer": "A",
    "analysis": "解析内容"
  },
  "timestamp": "2025-06-04T10:30:00Z"
}
```


---

**拍照搜题API v3.0 - 智能、高效、可靠的题目解析服务** 🚀

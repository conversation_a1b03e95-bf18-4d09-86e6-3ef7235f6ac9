<div class="row-content am-cf">
    <div class="row">
        <div class="am-u-sm-12 am-u-md-12 am-u-lg-12">
            <div class="widget am-cf">
                <div class="widget-head am-cf">
                    <div class="widget-title am-cf">搜索记录</div>
                </div>
                <div class="widget-body am-fr">
              
                    <div class="am-scrollable-horizontal am-u-sm-12">
                        <table width="100%" class="am-table am-table-compact am-table-striped
                         tpl-table-black ">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>用户</th>
                                <th>状态</th>
                                <th style="max-width:300px">关键词</th>
                                <th>搜索时间</th>
                            
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                                <?php  foreach ($list as $item): ?>
                                <tr>
                                    <td class="am-text-middle">
                                        <?= $item['id'] ?>
<!--                                        id-->
                                    </td>
                                    <td class="am-text-middle">
                                       
                                        <a href="<?= $item['avatar'] ?>" title="点击查看大图" target="_blank">
                                            <img src="<?= $item['avatar'] ?>" width="72" height="72" alt="">
                                        </a>
                                        <?= $item['nickname'] ?>
                                   
                                          
<!--                                    用户-->
                                    </td>
                                    <td class="am-text-middle">
                                         <?php if ($item['status'] == '0'): ?>
                                          <a style="color:red">失败</a>
                                          <?php endif; ?>
                                          <?php if ($item['status'] == '1'): ?>
                                          <a style="color:greed">成功</a>
                                          <?php endif; ?>
<!--                                          状态-->
                                       </td>
                                    
                                    <td class="am-text-middle" style="max-width:300px; word-wrap: break-word;" >
<!--                                    关键词-->
                                           <?php if ($item['type'] == '1'): ?>

                                        <a href="<?= $item['text'] ?>" title="点击查看大图" target="_blank">
                                            <img src="<?= $item['text'] ?>" width="72" height="72" alt="">
                                        </a>

                                          <?php endif; ?>

                                         <?php if ($item['type'] == '0'): ?>
                                       <div style="margin-bottom: 8px;">
                                             <?= $item['text'] ?>
                                       </div>
<!--                                        输出反序列化 `result` - 优化显示-->
                                        <?php if (!empty($item['result'])): ?>
                                            <?php $resultData = unserialize($item['result']); ?>
                                            <?php if (!empty($resultData)): ?>
                                                <div class="result-details" style="margin-top: 8px;">
                                                    <button type="button" class="am-btn am-btn-xs am-btn-secondary toggle-details"
                                                            onclick="toggleDetails(this)" style="margin-bottom: 5px;">
                                                        <i class="am-icon-eye"></i> 查看详情
                                                    </button>
                                                    <div class="details-content" style="display: none; max-height: 200px; overflow-y: auto;
                                                         background: #f8f9fa; padding: 8px; border-radius: 4px; font-size: 12px;
                                                         border: 1px solid #e9ecef;">
                                                        <?php if (is_array($resultData)): ?>
                                                            <?php foreach ($resultData as $index => $question): ?>
                                                                <div style="margin-bottom: 10px; padding-bottom: 8px; border-bottom: 1px solid #dee2e6;">
                                                                    <strong style="color: #495057;">题目 <?= $index + 1 ?>:</strong><br>
                                                                    <span style="color: #6c757d;">
                                                                        <?= htmlspecialchars($question['id'] ?? '无ID') ?>
                                                                    </span>
                                                                    <span style="color: #6c757d;">
                                                                        <?= htmlspecialchars($question['title'] ?? '无标题') ?>
                                                                    </span>
                                                                    <br>
                                                                    <?php if (!empty($question['content'])): ?>
                                                                        <small style="color: #868e96;">解析: <?= htmlspecialchars($question['content']) ?></small><br>
                                                                    <?php endif; ?>
                                                                    <?php if (!empty($question['dn'])): ?>
                                                                        <small style="color: #28a745;">答案: <?= htmlspecialchars($question['dn']) ?></small>
                                                                    <?php endif; ?>
                                                                </div>
                                                            <?php endforeach; ?>
                                                        <?php else: ?>
                                                            <pre style="white-space: pre-wrap; margin: 0; font-size: 11px;"><?= htmlspecialchars(json_encode($resultData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) ?></pre>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php endif; ?>
                                        <?php endif; ?>



                                          <?php endif; ?>
                                    </td>
                                    <td class="am-text-middle"><?= $item['add_time'] ?></td>
                              
                           
                                    <td class="am-text-middle">
                                        <div class="tpl-table-black-operation" onclick="del(<?= $item['id'] ?>)">
                                    
                                         
                                                <a>
                                                    <i class="am-icon-trash"></i> 删除
                                                </a>
                                        
                                  
                                        </div>
                                    </td>
                                </tr>
                                  <?php endforeach;  ?>
                                <tr>
                                    <td colspan="13" class="am-text-center"></td>
                                </tr>
                        
                            </tbody>
                        </table>
                    </div>
                    <div class="am-u-lg-12 am-cf">
                        <div class="am-fr"></div>
                        <div class="am-fr pagination-total am-margin-right">
                            <div class="am-vertical-align-middle">总记录：<?= $num ?>条</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    function del (id)

{
       layer.confirm('删除后不可恢复，确定要删除吗？', function (index) {


                        $.ajax({
                            url:"/index.php?s=/store/index/del",
                            type:"post",
                            data:{
                                id:id
                            },
                            success(res)
                            {
                                console.log(res)
                                var data=JSON.parse(res)

                                if(data.code==1)
                                {

                                    layer.msg('删除成功')
                                }else{

                                     layer.msg('网络发生错误')
                                }

                                window.location.href=''

                            }
                        })






                    layer.close(index);




                });
}

    // 切换详情显示/隐藏
    function toggleDetails(button) {
        var detailsContent = button.nextElementSibling;
        var icon = button.querySelector('i');

        if (detailsContent.style.display === 'none') {
            detailsContent.style.display = 'block';
            button.innerHTML = '<i class="am-icon-eye-slash"></i> 隐藏详情';
            button.className = 'am-btn am-btn-xs am-btn-warning toggle-details';
        } else {
            detailsContent.style.display = 'none';
            button.innerHTML = '<i class="am-icon-eye"></i> 查看详情';
            button.className = 'am-btn am-btn-xs am-btn-secondary toggle-details';
        }
    }






    $(function () {

        /**
         * 注册操作事件
         * @type {jQuery|HTMLElement}
         */
        var $dropdown = $('.j-opSelect');
        $dropdown.dropdown();
        $dropdown.on('click', 'li a', function () {
            var $this = $(this);
            var id = $this.parent().parent().data('id');
            var type = $this.data('type');
            if (type === 'delete') {
                layer.confirm('删除后不可恢复，确定要删除吗？', function (index) {
                    $.post("index.php?s=/store/apps.dealer.user/delete", {dealer_id: id}, function (result) {
                        result.code === 1 ? $.show_success(result.msg, result.url)
                            : $.show_error(result.msg);
                    });
                    layer.close(index);
                });
            }
            $dropdown.dropdown('close');
        });

        // 删除元素
        var url = "<?= url('user/delete') ?>";
        $('.j-delete').delete('user_id', url, '删除后不可恢复，确定要删除吗？');

    });
</script>


<?php
/**
 * 会员卡生成测试脚本
 * 模拟生成1张次数为100的次卡
 */

// 模拟测试数据
$test_data = [
    'number' => 1,        // 生成数量：1张
    'second' => 100,      // 次数：100次  
    'type' => 10,         // 卡密类型：次卡
    'make_uid' => 11547,  // 代理商ID（从数据库示例中获取）
    'wxapp_id' => 10001   // 小程序ID（从数据库示例中获取）
];

echo "=== 会员卡生成测试 ===\n";
echo "测试参数：\n";
echo "- 生成数量：{$test_data['number']} 张\n";
echo "- 次数：{$test_data['second']} 次\n";
echo "- 卡密类型：次卡 (type=10)\n";
echo "- 代理商ID：{$test_data['make_uid']}\n";
echo "- 小程序ID：{$test_data['wxapp_id']}\n\n";

// 模拟卡密生成逻辑
function generateCardCode() {
    return 'YC' . substr(time(), -3) . substr(microtime(), 2, 5) . sprintf('%02d', rand(0, 99));
}

// 模拟文件名生成
function generateFileName($wxapp_id) {
    return md5(md5($wxapp_id)) . ".txt";
}

// 执行测试
echo "=== 执行测试 ===\n";

// 1. 生成卡密
$card_code = generateCardCode();
echo "1. 生成卡密：{$card_code}\n";

// 2. 生成文件名
$filename = generateFileName($test_data['wxapp_id']);
echo "2. 文件名：{$filename}\n";

// 3. 模拟数据库记录
$db_record = [
    'title' => '次卡',
    'type' => $test_data['type'],
    'number' => $test_data['second'],  // 次数
    'second' => 0,                     // 默认值
    'code' => $card_code,
    'make_uid' => $test_data['make_uid'],
    'wxapp_id' => $test_data['wxapp_id'],
    'create_time' => time(),
    'update_time' => time(),
    'use_uid' => 0,
    'use_time' => 0
];

echo "3. 数据库记录：\n";
foreach ($db_record as $key => $value) {
    echo "   {$key}: {$value}\n";
}

// 4. 模拟文件内容
$file_content = $card_code . "\n";
echo "4. 文件内容：{$file_content}";

// 5. 模拟URL生成
$download_url = "/index.php?s=/store/apps/card.index/down";
echo "5. 下载URL：{$download_url}\n";

echo "\n=== 测试完成 ===\n";
echo "预期结果：\n";
echo "✓ 数据库中新增1条会员卡记录\n";
echo "✓ 生成临时文件包含卡密\n";
echo "✓ 页面跳转到下载链接\n";
echo "✓ 用户可以下载包含卡密的文件\n";
echo "✓ 下载完成后临时文件被删除\n";

// 6. 检查可能的问题
echo "\n=== 潜在问题检查 ===\n";

// 检查文件权限
if (!is_writable('./')) {
    echo "⚠️  警告：当前目录不可写，文件生成可能失败\n";
} else {
    echo "✓ 当前目录可写\n";
}

// 检查函数是否存在
if (!function_exists('mb_rand_str')) {
    echo "⚠️  警告：mb_rand_str 函数不存在（用于非次卡类型）\n";
} else {
    echo "✓ mb_rand_str 函数存在\n";
}

echo "\n=== 实际测试步骤 ===\n";
echo "1. 访问：/index.php?s=/store/apps.card.index/add\n";
echo "2. 填写表单：\n";
echo "   - 生成数量：1\n";
echo "   - 生成次数：100\n";
echo "   - 选择代理：[选择任意代理商]\n";
echo "   - 生成类型：次卡\n";
echo "3. 点击提交\n";
echo "4. 检查是否跳转到下载页面\n";
echo "5. 检查文件是否能正常下载\n";
echo "6. 检查数据库是否有新记录\n";

?>

# 云搜索功能实现说明 (v2.0)

## 概述

本文档说明了云搜索功能的实现，该功能通过调用外部云题库API来扩展本地题库的搜索能力。

**v2.0 重要更新**:
- 请求方法从GET改为POST
- API地址更新为 `http://tiku.uzdns.com/api`
- 鉴权方式仅支持Header传递API密钥
- 请求参数放在JSON请求体中

## 实现特性

1. **多级搜索策略**: 优先使用本地题库，无结果时自动调用云API
2. **配置化管理**: 通过后台设置页面配置API地址和密钥
3. **数据格式统一**: 云API返回数据自动转换为与本地搜索一致的格式
4. **错误处理**: 完善的异常处理和日志记录
5. **向后兼容**: 不影响现有的本地搜索功能
6. **POST请求**: 使用POST方法和JSON请求体，符合API v2.0规范

## 核心文件修改

### 1. 控制器文件 (`source/application/api/controller/answer/Index.php`)

#### 新增方法
- `longApiCloud($text)`: 云API搜索方法

#### 修改方法
- `textSearch($text)`: 增加云API搜索逻辑
- `textSearch1($text)`: 增加云API搜索逻辑

#### 搜索流程
```
1. 根据配置选择本地搜索方式（LocalCloud 或 longRangeCloud）
2. 如果本地搜索无结果，尝试另一种本地搜索方式
3. 如果仍无结果，调用云API搜索 (longApiCloud)
4. 返回搜索结果或错误信息
```

### 2. 设置模型 (`source/application/common/model/Setting.php`)

#### 新增配置项
- `cloud_api_key`: 云题库API密钥
- `cloud_api_url`: 云题库API基础地址

### 3. 后台设置页面 (`source/application/store/view/setting/ocr.php`)

#### 新增配置界面
- 云API地址配置
- 云API密钥配置

## API调用详情

### 请求格式 (v2.0)
```
POST {api_url}/tiku/search
Content-Type: application/json
X-API-Key: {api_key}

{
    "keyword": "{text}",
    "limit": 5
}
```

### 响应格式转换

#### 云API原始响应
```json
{
    "code": 200,
    "message": "搜索成功",
    "data": {
        "items": [
            {
                "title": "题目标题",
                "options": {
                    "A": "选项A",
                    "B": "选项B",
                    "C": "选项C",
                    "D": "选项D"
                },
                "answer": "A",
                "explanation": "答案解析",
                "image_url": "图片地址"
            }
        ]
    }
}
```

#### 转换后格式（与本地搜索一致）
```json
[
    {
        "title": "题目标题",
        "a": "选项A",
        "b": "选项B", 
        "c": "选项C",
        "d": "选项D",
        "dn": "A",
        "content": "答案解析",
        "imgUrl": "图片地址"
    }
]
```

## 配置说明

### 后台配置路径
系统设置 → OCR设置 → 云题库API配置

### 配置项说明
- **云API地址**: 云题库API的基础地址，默认：`http://tiku.uzdns.com/api`
- **云API密钥**: 访问云题库API的密钥，默认：`tk_30jVXSiUhicSgEIGaf9peCJH0VdJqlc8`

## 错误处理

### 异常情况处理
1. **网络连接失败**: 返回false，继续使用本地搜索结果
2. **API响应错误**: 记录错误日志，返回false
3. **数据格式错误**: 记录错误日志，返回false
4. **API密钥无效**: 记录错误日志，返回false

### 日志记录
错误信息会记录到系统日志中，类型为 `cloud-api-error`

## 测试

### 测试脚本
项目根目录下的 `test_cloud_search.php` 可用于测试云搜索功能。

### 运行测试
```bash
php test_cloud_search.php
```

## 使用流程

1. **配置API**: 在后台设置页面配置云API地址和密钥
2. **自动调用**: 当本地搜索无结果时，系统自动调用云API
3. **结果展示**: 云API结果与本地结果使用相同的展示格式

## 注意事项

1. **API限制**: 注意云API的调用频率限制
2. **网络依赖**: 云搜索依赖网络连接，网络异常时会降级到本地搜索
3. **数据一致性**: 确保云API返回的数据格式符合预期
4. **性能影响**: 云API调用会增加响应时间，建议优化本地搜索以减少云API调用

## v2.0 更新说明

### 主要变更

1. **请求方法变更**:
   - 从GET请求改为POST请求
   - 所有参数放在JSON请求体中

2. **鉴权方式变更**:
   - 移除查询参数方式的API密钥传递
   - 仅支持Header方式：`X-API-Key` 或 `Authorization: Bearer`

3. **API地址更新**:
   - 从 `http://tiku.local.gd/api` 更新为 `http://tiku.uzdns.com/api`

4. **请求头要求**:
   - 必须设置 `Content-Type: application/json`
   - 必须通过Header传递API密钥

### 代码变更

1. **新增方法**: `curlPostWithHeaders()` - 专门处理带Header的POST请求
2. **增强日志**: 更详细的错误日志和成功日志记录
3. **错误处理**: 更完善的HTTP状态码和cURL错误检查

### 兼容性说明

- **不向后兼容**: 旧版本的GET请求方式将无法工作
- **配置迁移**: 需要更新API地址配置
- **测试更新**: 测试脚本已更新为POST方法

## 扩展建议

1. **缓存机制**: 可以考虑对云API结果进行缓存
2. **负载均衡**: 支持多个云API地址的负载均衡
3. **统计分析**: 记录云API调用统计信息
4. **配置优化**: 支持更细粒度的搜索配置
5. **请求重试**: 添加失败重试机制
6. **超时配置**: 支持可配置的请求超时时间

# 会员卡生成404错误修复总结

## 问题分析

1. **URL生成错误**：原来的 `url("apps.card.index/down")` 路径不正确
2. **文件生成不完整**：非次卡类型（月卡、季卡、年卡）没有生成下载文件
3. **错误处理缺失**：下载方法没有检查文件是否存在
4. **JavaScript逻辑错误**：页面跳转逻辑有冲突

## 修复内容

### 1. 修复URL生成 (source/application/store/controller/apps/card/Index.php:45)
```php
// 修复前
return $this->renderSuccess('添加成功', url("apps.card.index/down"));

// 修复后  
return $this->renderSuccess('添加成功', url("apps/card.index/down"));
```

### 2. 增强下载方法错误处理 (source/application/store/controller/apps/card/Index.php:50-73)
```php
public function down()
{
    $file_url = "./" . md5(md5($this->getWxappId())) . ".txt";
    
    // 检查文件是否存在
    if (!file_exists($file_url)) {
        return $this->renderError('文件不存在或已被删除');
    }
    
    // 检查文件大小
    $filesize = filesize($file_url);
    if ($filesize === false || $filesize === 0) {
        return $this->renderError('文件为空或无法读取');
    }
    
    Header("Content-type: application/octet-stream");
    header('content-type:application/octet-stream');
    header('content-disposition:attachment; filename=' . basename($file_url));
    header('content-length:' . $filesize);
    readfile($file_url);
    
    // 下载完成后删除临时文件
    @unlink($file_url);
}
```

### 3. 修复文件生成逻辑 (source/application/common/model/apps/Card.php:50-61)
```php
// 为所有卡密类型都生成下载文件
for ($i = 0; $i <= $number - 1; $i++) {
    $data[$i]['title'] = CardType::data()[$type]['name'];
    $data[$i]['type'] = $type;
    $data[$i]['second'] = $second;
    $data[$i]['number'] = CardType::data()[$type]['number'];
    $data[$i]['code'] = mb_rand_str(10) . $i;
    $data[$i]['make_uid'] = $user_id;
    $data[$i]['wxapp_id'] = self::$wxapp_id;
    // 将卡密写入文件
    file_put_contents(md5(md5(self::$wxapp_id)) . ".txt", $data[$i]['code'] . PHP_EOL, FILE_APPEND);
}
```

### 4. 修复JavaScript跳转逻辑 (source/application/store/view/apps/card/index/add.php:78-87)
```javascript
$('#my-form').superForm({success:function (res){
    $.show_success(res.msg)
    setTimeout(function (){
        if(res.url) {
            window.location.href = res.url
        } else {
            window.location.href = "/index.php?s=/store/apps.card.index/index"
        }
    },1500)
}});
```

### 5. 更新菜单权限配置 (source/application/store/extra/menus.php:186-191)
```php
'uris' => [
    'apps.card.index/index',
    'apps.card.index/add',
    'apps.card.index/down',  // 新增下载权限
    'apps.card.index/export',
]
```

## 测试步骤

1. 登录管理后台
2. 进入 应用中心 -> 会员卡 -> 生成记录
3. 点击"新增"按钮
4. 填写表单：
   - 选择代理商
   - 输入生成数量
   - 输入生成次数（如果是次卡）
   - 选择卡密类型
5. 提交表单
6. 检查是否能正常跳转到下载页面
7. 检查文件是否能正常下载

## 可能的访问路径

- 列表页面：`/index.php?s=/store/apps.card.index/index`
- 添加页面：`/index.php?s=/store/apps.card.index/add`
- 下载页面：`/index.php?s=/store/apps.card.index/down`

## 注意事项

1. 确保有足够的文件写入权限
2. 确保临时文件目录可写
3. 下载完成后会自动删除临时文件
4. 如果仍有问题，检查服务器错误日志

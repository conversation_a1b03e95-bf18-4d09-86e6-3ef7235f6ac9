<?php

namespace app\store\model;

use app\common\model\ReturnAddress as ReturnAddressModel;

/**
 * 退货地址模型
 * Class ReturnAddress
 * @package app\store\model
 */
class ReturnAddress extends ReturnAddressModel
{
    /**
     * 获取列表
     * @return \think\Paginator
     * @throws \think\exception\DbException
     */
    public function getList()
    {
        return $this->order(['sort' => 'asc', $this->getPk() => 'desc'])
            ->where('is_delete', '=', 0)
            ->paginate(15, false, [
                'query' => \request()->request()
            ]);
    }

    /**
     * 获取全部收货地址
     * @return false|\PDOStatement|string|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getAll()
    {
        return $this->order(['sort' => 'asc', $this->getPk() => 'desc'])
            ->where('is_delete', '=', 0)
            ->select();
    }

    /**
     * 添加新记录
     * @param $data
     * @return false|int
     */
    public function add($data)
    {
        $data['wxapp_id'] = self::$wxapp_id;
        return $this->allowField(true)->save($data);
    }

    /**
     * 编辑记录
     * @param $data
     * @return bool|int
     */
    public function edit($data)
    {
        return $this->allowField(true)->save($data);
    }

    /**
     * 删除记录
     * @return bool|int
     */
    public function remove()
    {
        return $this->save(['is_delete' => 1]);
    }

}
   <!-- 内容区域 start -->
    <div class="tpl-content-wrapper " style="margin-left: -20px">
        <div class="row-content am-cf">
    <div class="row">
        <div class="am-u-sm-12 am-u-md-12 am-u-lg-12">
            <div class="widget am-cf">
                <div class="widget-head am-cf">
                    <div class="widget-title am-cf">搜题记录</div>
                </div>
                <div class="widget-body am-fr">
                    <!-- 工具栏 -->
                    <div class="page_toolbar am-margin-bottom-xs am-cf">
                        <div class="am-form-group">
                            <div class="am-btn-toolbar">
                                <div class="am-btn-group am-btn-group-xs">
                                    <a class="am-btn am-btn-default am-btn-success am-radius"
                                       href="index.php?s=/store/index/add_tiku">
                                        <span class="am-icon-plus"></span> 新增
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="am-scrollable-horizontal am-u-sm-12">
                        <table width="100%" class="am-table am-table-compact am-table-striped
                         tpl-table-black">
                            <thead>
                            <tr>
                                <th>记录ID</th>
                                <th>题目</th>
                             
                                <!--<th>解析</th>-->
                                <th>识别图片</th>
                                <th>目前状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                            </thead>
                            <tbody>
                                    <?php  foreach ($res as $val): ?>
                                 <tr>
                                    <td class="am-text-middle"><?= $val['question_id'] ?></td>
                                     <td class="am-text-middle"
                                      
                                     >
                                        <div >
                                               <?= $val['subject'] ?> 
                                        </div> 
                                      
                                     </td>
                                
                                    <!--<td class="am-text-middle">-->
                                    <!--    <div class="" style="width: 200px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap;">-->
                                    <!--             <?= $val['solve'] ?>                            -->
                                    <!--                                           </div>-->
                                    <!--</td>-->
                                    <td class="am-text-middle">
                                        
                                          <?php if ($val['imgUrl']  == ''): ?>
                                          
                                           未上传
                                          <?php endif; ?>
                                              <?php if ($val['imgUrl']  != ''): ?>
                                             <a href="<?= $val['imgUrl'] ?>" title="点击查看大图" target="_blank">
                                            <img src="<?= $val['imgUrl'] ?>" width="72" height="72" alt="">
                                        </a>
                                        
                                          
                                          <?php endif; ?>
                                     
                                    </td>
                                    <td class="am-text-middle">
                                       <span class="am-badge am-badge-success">
                                           识别正常                                       </span>
                                    </td>
                                    <td class="am-text-middle"><?= date('Y-m-d h:i:s',$val['create_time'] )?></td>
                                    <td class="am-text-middle">
                                        <div class="tpl-table-black-operation">
                          <a class="j-delete tpl-table-black-operation-default"
                                                   href="javascript:void(0);" data-id="<?= $val['question_id'] ?>">
                                                    <i class="am-icon-trash"></i> 删除
                                                </a>
                                                                                    </div>
                                    </td>
                                </tr>
                              
                                                    
                 <?php endforeach;?>
                                             
                                                     
                                  </tbody>
                        </table>
                    </div>
         
                    <div class="am-u-lg-12 am-cf">
                        <div class="am-fr">
                            <ul class="pagination">
                                <li class="">
                                 <a href="/index.php?s=/store/index/tiku&&now_page=<?= $last_page ?>"> <span>&laquo;</span></a>  
                        </li> 
                        <?php for($i=$now_page;$i<$now_page+10;$i++): ?>
                            
                            <?php if($i==$now_page): ?>
                        <li class="active">
                            <a href="/index.php?s=/store/index/tiku&&now_page=<?= $i ?>">
                            <span><?= $i ?></span>
                            </a>
                            </li>
                          <?php endif; ?>
                          
                              <?php if($i!=$now_page): ?>
                              <li class="">
                           <a href="/index.php?s=/store/index/tiku&&now_page=<?= $i ?>">
                          <span><?= $i ?></span>
                          </a>
                          </li>
                          <?php endif; ?>
                        
                        <?php endfor; ?>
                              <li class="">
                           <a >
                          <span>...</span>
                          </a>
                          </li>
                         
                        
                            <li><a href="/index.php?s=/store/index/tiku&&now_page=<?= $next_page ?>">&raquo;</a></li>
                            </ul> 
                            </div>
                        <div class="am-fr pagination-total am-margin-right">
                            <div class="am-vertical-align-middle">总记录：<?= $num ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    $(function () {
        // 删除元素
        var url = "index.php?s=/store/index/delete_tiku";
        $('.j-delete').delete('id', url);
    });
</script>

    </div>
    <!-- 内容区域 end -->


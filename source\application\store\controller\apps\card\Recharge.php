<?php


namespace app\store\controller\apps\card;


use app\store\controller\Controller;
use app\store\model\Setting as SettingModel;

class Recharge extends Controller
{
    /**
     * 充值设置
     * @return array|bool|mixed
     * @throws \think\exception\DbException
     */
    public function setting()
    {
        if (!$this->request->isAjax()) {
            $values = SettingModel::getItem('recharge');
            return $this->fetch('setting', ['values' => $values]);
        }
        $model = new SettingModel;
        if ($model->edit('recharge', $this->postData('recharge'))) {
            return $this->renderSuccess('操作成功');
        }
        return $this->renderError($model->getError() ?: '操作失败');
    }
}
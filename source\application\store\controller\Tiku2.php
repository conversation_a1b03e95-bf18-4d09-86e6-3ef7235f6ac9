<?php

namespace app\store\controller;

use app\common\service\JiebaTokenizer;
use app\common\service\TNTSearchService;
use app\store\model\camera\Cloud;
use app\store\model\camera\Exam;
use app\store\model\camera\Question;
use app\store\model\camera\Subject;
use app\store\model\Store as StoreModel;
use think\Config;
use think\Db;

/**
 * 新版题库
 * Class Index
 * @package app\store\controller
 */
class Tiku2 extends Controller
{
    /**
     * 后台首页
     * @return mixed
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function index()
    {

        return $this->fetch('index');
    }

    public function upload()
    {

        $file_name = $_FILES['file']['name'];
        $file_type = $_FILES['file']['type'];
        $file_size = $_FILES['file']['size'];
        $file_tmp_name = $_FILES['file']['tmp_name'];
        $time = time();


        if ($file_type == "image/png" || $file_type == "image/jpeg") {
            $logo = $time . $file_name;

            move_uploaded_file($file_tmp_name, "../web/" . $logo);

            $result = [
                'msg' => 'ok',
                'code' => 1,
                'src' => "http://" . $_SERVER['HTTP_HOST'] . "/" . $logo,
                'size' => $file_size
            ];

            return  json_encode($result);
        } else {
            $result = [
                'msg' => 'file type err',
                'code' => 0
            ];
            return json_encode($result);
        }
    }


    public function delete_tiku($id)
    {
        // 只删除 yoshop_tiku 表记录
        $res = Db::execute("delete from yoshop_tiku where id='$id'");
        if ($res > 0) {
            return $this->renderSuccess('题目删除成功');
        }
        return $this->renderError('删除失败');
    }
    public function tiku()
    {
        // 获取搜索关键词
        $search = input('search', '');
        $search = trim($search);

        // 构建查询条件
        $where = '';
        $params = [];

        if (!empty($search)) {
            // 使用 LIKE 进行模糊搜索（如果没有全文索引）
            $where = "WHERE (title LIKE ? OR subject LIKE ? OR optionsA LIKE ? OR optionsB LIKE ? OR optionsC LIKE ? OR optionsD LIKE ?)";
            $searchParam = '%' . $search . '%';
            $params = [$searchParam, $searchParam, $searchParam, $searchParam, $searchParam, $searchParam];
        }

        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM yoshop_tiku " . $where;
        $countRes = Db::query($countSql, $params);
        $num = $countRes[0]['total'];

        //=渲染列表================================================================================================================
        $len = 10;   //每页长度
        $page = $num > 0 ? ceil($num / $len) : 1; // 确保至少有1页

        if (isset($_GET['now_page'])) {
            $now_page = max(1, intval($_GET['now_page'])); // 确保页码至少为1
        } else {
            $now_page = 1;
        } //设定当前页

        // 确保当前页不超过总页数
        if ($now_page > $page) {
            $now_page = $page;
        }

        $last_page = $now_page - 1;
        if ($last_page < 1) {
            $last_page = 1;
        } //上一页
        $next_page = $now_page + 1;
        if ($next_page > $page) {
            $next_page = $page;
        } //下一页
        $limit = ($now_page - 1) * $len;

        // 构建查询SQL
        if (!empty($search)) {
            // 带搜索的查询
            $sql = "SELECT * FROM yoshop_tiku " . $where . " ORDER BY id DESC LIMIT $limit,$len";
            $res1 = Db::query($sql, $params);
        } else {
            // 普通查询
            $res1 = Db::query("SELECT * FROM yoshop_tiku ORDER BY id DESC LIMIT $limit,$len");
        }

        // 传递变量到视图
        $this->assign("now_page", $now_page);
        $this->assign("last_page", $last_page);
        $this->assign("next_page", $next_page);
        $this->assign("num", $num);
        $this->assign("page", $page);
        $this->assign("len", $len);
        $this->assign("search", $search);
        $this->assign("res", $res1);

        return $this->fetch('tiku');
    }
    public function add_tiku()
    {



        return $this->fetch('add_tiku');
    }
    public function add_tiku_do()
    {
        $index = $_POST['data'];

        $timu = $index['timu'];
        $imgUrl = $index['imgUrl'];
        $a = $index['a'];
        $b = $index['b'];
        $c = $index['c'];
        $d = $index['d'];
        $e = $index['e'];
        $f = $index['f'];
        $daan = $index['daan'];
        $jiexi = $index['jiexi'];
        $kemu = $index['kemu'];

        //  $data['options']='A:'.$a.",B:".$b.",C:".$c.",D:".$d.",E:".$e.",F:".$f;
        //  $data['question']=$timu;
        $data['solve'] = $jiexi; //解析
        $data['imgUrl'] = $imgUrl;

        $data['type'] = 1; //选择题
        $data['answer'] = 0;

        // $daanarr = explode(',', $daan);






        $data['answer'] = $daan;
        /*var_dump($answerstr);
                      //exit();
                      if($daan=='A')
                      {
                          $data['answer']=1;
                      }
                      if($daan=='B')
                      {
                          $data['answer']=2;
                      }
                          if($daan=='C')
                      {
                          $data['answer']=3;
                      }
                          if($daan=='D')
                      {
                          $data['answer']=4;
                      }
                          if($daan=='E')
                      {
                          $data['answer']=5;
                      }
                          if($daan=='F')
                      {
                          $data['answer']=6;
                      }*/
        $data['subject'] = $timu;
        //  $data['model']='car';
        //  $data['subject_type']=$kemu;
        $data['optionA'] = $a;
        $data['optionB'] = $b;
        $data['optionC'] = $c;
        $data['optionD'] = $d;
        $data['optionE'] = $e;
        $data['optionF'] = $f;
        // 准备 yoshop_tiku 表数据
        $data_tiku = [];
        $data_tiku['title'] = $timu;
        $data_tiku['subject'] = $timu;
        $data_tiku['optionsA'] = $a;
        $data_tiku['optionsB'] = $b;
        $data_tiku['optionsC'] = $c;
        $data_tiku['optionsD'] = $d;
        $data_tiku['optionsE'] = $e;
        $data_tiku['optionsF'] = $f;
        $data_tiku['answer'] = $daan;
        $data_tiku['explanation'] = $jiexi;
        if(!empty($imgUrl)) {
            $data_tiku['image_url'] = $imgUrl;
        }
        $data_tiku['created_time'] = time();
        $data_tiku['updated_time'] = time();

        // 只插入到 yoshop_tiku 表
        $res = Db::name("tiku")->insert($data_tiku);
        if ($res > 0) {
            $result = [
                'msg' => '题目添加成功',
                'code' => 1
            ];
            echo json_encode($result);
        } else {
            $result = [
                'msg' => '题目添加失败',
                'code' => 0
            ];
            echo json_encode($result);
        }
    }

    /**
     * 编辑题目页面
     * @param int $id 题目ID
     * @return mixed
     */
    public function edit_tiku($id)
    {
        // 获取题目信息
        $tiku = Db::query("SELECT * FROM yoshop_tiku WHERE id = ?", [$id]);
        if (empty($tiku)) {
            return $this->renderError('题目不存在');
        }

        $this->assign('tiku', $tiku[0]);
        return $this->fetch('edit_tiku');
    }

    /**
     * 处理编辑题目提交
     * @return string
     */
    public function edit_tiku_do()
    {
        $index = $_POST['data'];
        $id = $index['id'];

        // 验证题目是否存在
        $existing = Db::query("SELECT id FROM yoshop_tiku WHERE id = ?", [$id]);
        if (empty($existing)) {
            $result = [
                'msg' => '题目不存在',
                'code' => 0
            ];
            return json_encode($result);
        }

        $timu = $index['timu'];
        $imgUrl = $index['imgUrl'];
        $a = $index['a'];
        $b = $index['b'];
        $c = $index['c'];
        $d = $index['d'];
        $e = $index['e'];
        $f = $index['f'];
        $daan = $index['daan'];
        $jiexi = $index['jiexi'];

        // 准备更新数据
        $data_tiku = [];
        $data_tiku['title'] = $timu;
        $data_tiku['subject'] = $timu;
        $data_tiku['optionsA'] = $a;
        $data_tiku['optionsB'] = $b;
        $data_tiku['optionsC'] = $c;
        $data_tiku['optionsD'] = $d;
        $data_tiku['optionsE'] = $e;
        $data_tiku['optionsF'] = $f;
        $data_tiku['answer'] = $daan;
        $data_tiku['explanation'] = $jiexi;
        if(!empty($imgUrl)) {
            $data_tiku['image_url'] = $imgUrl;
        }
        $data_tiku['updated_time'] = time();

        // 更新 yoshop_tiku 表
        $res = Db::name("tiku")->where('id', $id)->update($data_tiku);
        if ($res !== false) {
            $result = [
                'msg' => '题目编辑成功',
                'code' => 1
            ];
            echo json_encode($result);
        } else {
            $result = [
                'msg' => '题目编辑失败',
                'code' => 0
            ];
            echo json_encode($result);
        }
    }
}
